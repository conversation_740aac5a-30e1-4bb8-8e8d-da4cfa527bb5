
-- Remove image column from speakers table since we'll only upload images during proposal creation
ALTER TABLE speakers DROP COLUMN IF EXISTS image;

-- Remove speaker_images table since we're not storing speaker images separately anymore
DROP TABLE IF EXISTS speaker_images;

-- Add a settings table for storing email configuration like stamp mailbox
CREATE TABLE IF NOT EXISTS email_settings (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users NOT NULL,
  stamp_mailbox TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable RLS on email_settings
ALTER TABLE email_settings ENABLE ROW LEVEL SECURITY;

-- Create policies for email_settings
CREATE POLICY "Users can view their own email settings" 
  ON email_settings 
  FOR SELECT 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own email settings" 
  ON email_settings 
  FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own email settings" 
  ON email_settings 
  FOR UPDATE 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own email settings" 
  ON email_settings 
  FOR DELETE 
  USING (auth.uid() = user_id);

-- Create a function to get today's birthdays (for the automated email system)
CREATE OR REPLACE FUNCTION get_todays_birthdays()
RETURNS TABLE (
  id UUID,
  name TEXT,
  email TEXT,
  date_of_birth DATE
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT s.id, s.name, s.email, s.date_of_birth
  FROM speakers s
  WHERE s.date_of_birth IS NOT NULL
    AND EXTRACT(MONTH FROM s.date_of_birth) = EXTRACT(MONTH FROM CURRENT_DATE)
    AND EXTRACT(DAY FROM s.date_of_birth) = EXTRACT(DAY FROM CURRENT_DATE);
END;
$$;
