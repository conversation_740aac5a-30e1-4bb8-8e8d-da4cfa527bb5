
-- Create employees table
CREATE TABLE public.employees (
  id uuid NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name text NOT NULL,
  email text NOT NULL UNIQUE,
  role text,
  image_url text,
  created_at timestamptz NOT NULL DEFAULT now()
);

-- Create speakers table
CREATE TABLE public.speakers (
  id uuid NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name text NOT NULL,
  email text NOT NULL UNIQUE,
  bio text,
  category text,
  availability text,
  rate integer,
  topics text[],
  image_url text,
  created_at timestamptz NOT NULL DEFAULT now()
);

-- Create events table
CREATE TABLE public.events (
  id uuid NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  title text NOT NULL,
  date timestamptz NOT NULL,
  location text,
  description text,
  status text,
  responsible_person_id uuid REFERENCES public.employees(id) ON DELETE SET NULL,
  created_at timestamptz NOT NULL DEFAULT now()
);

-- Create proposals table
CREATE TABLE public.proposals (
  id uuid NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  speaker_id uuid REFERENCES public.speakers(id) ON DELETE CASCADE,
  event_name text,
  status text,
  submitted_date date,
  details jsonb,
  created_at timestamptz NOT NULL DEFAULT now()
);

-- Enable Row Level Security (RLS) for all tables
ALTER TABLE public.employees ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.speakers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.events ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.proposals ENABLE ROW LEVEL SECURITY;

-- Create policies to allow authenticated users to manage data
CREATE POLICY "Authenticated users can manage employees" ON public.employees FOR ALL USING (auth.role() = 'authenticated');
CREATE POLICY "Authenticated users can manage speakers" ON public.speakers FOR ALL USING (auth.role() = 'authenticated');
CREATE POLICY "Authenticated users can manage events" ON public.events FOR ALL USING (auth.role() = 'authenticated');
CREATE POLICY "Authenticated users can manage proposals" ON public.proposals FOR ALL USING (auth.role() = 'authenticated');
