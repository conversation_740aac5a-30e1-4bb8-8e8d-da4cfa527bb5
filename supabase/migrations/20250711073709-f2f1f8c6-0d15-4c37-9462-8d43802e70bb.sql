-- Create proposal_templates table for advanced template customization
CREATE TABLE public.proposal_templates (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL,
  name TEXT NOT NULL,
  cover_page_title TEXT NOT NULL,
  cover_page_subtitle TEXT,
  cover_page_image_url TEXT,
  about_us_mission TEXT NOT NULL,
  
  -- Colors
  primary_color TEXT NOT NULL DEFAULT '#3B82F6',
  secondary_color TEXT NOT NULL DEFAULT '#1E40AF',
  accent_color TEXT NOT NULL DEFAULT '#F59E0B',
  text_color TEXT NOT NULL DEFAULT '#1F2937',
  background_color TEXT NOT NULL DEFAULT '#FFFFFF',
  
  -- Typography
  heading_font TEXT NOT NULL DEFAULT 'Montserrat, sans-serif',
  body_font TEXT NOT NULL DEFAULT 'Open Sans, sans-serif',
  font_size_base INTEGER NOT NULL DEFAULT 12,
  line_height NUMERIC NOT NULL DEFAULT 1.5,
  
  -- Layout
  page_margin INTEGER NOT NULL DEFAULT 20,
  section_spacing INTEGER NOT NULL DEFAULT 15,
  header_height INTEGER NOT NULL DEFAULT 80,
  footer_height INTEGER NOT NULL DEFAULT 50,
  
  -- Page Structure
  include_cover_page BOOLEAN NOT NULL DEFAULT true,
  include_about_page BOOLEAN NOT NULL DEFAULT true,
  include_event_details BOOLEAN NOT NULL DEFAULT true,
  include_speaker_profiles BOOLEAN NOT NULL DEFAULT true,
  include_investment_summary BOOLEAN NOT NULL DEFAULT true,
  include_thank_you_page BOOLEAN NOT NULL DEFAULT true,
  
  -- Content Options
  show_speaker_images BOOLEAN NOT NULL DEFAULT true,
  show_speaker_bios BOOLEAN NOT NULL DEFAULT true,
  show_speaker_rates BOOLEAN NOT NULL DEFAULT true,
  show_company_logo BOOLEAN NOT NULL DEFAULT true,
  watermark_text TEXT,
  
  -- Advanced Layout
  layout_style TEXT NOT NULL DEFAULT 'modern' CHECK (layout_style IN ('classic', 'modern', 'minimal', 'creative')),
  speaker_layout TEXT NOT NULL DEFAULT 'cards' CHECK (speaker_layout IN ('grid', 'list', 'cards')),
  image_style TEXT NOT NULL DEFAULT 'rounded' CHECK (image_style IN ('square', 'circle', 'rounded')),
  
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE public.proposal_templates ENABLE ROW LEVEL SECURITY;

-- Create policies for user access
CREATE POLICY "Users can view their own templates" 
ON public.proposal_templates 
FOR SELECT 
USING (auth.uid() = user_id::uuid);

CREATE POLICY "Users can create their own templates" 
ON public.proposal_templates 
FOR INSERT 
WITH CHECK (auth.uid() = user_id::uuid);

CREATE POLICY "Users can update their own templates" 
ON public.proposal_templates 
FOR UPDATE 
USING (auth.uid() = user_id::uuid);

CREATE POLICY "Users can delete their own templates" 
ON public.proposal_templates 
FOR DELETE 
USING (auth.uid() = user_id::uuid);

-- Create function to update timestamps
CREATE OR REPLACE FUNCTION public.update_proposal_templates_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for automatic timestamp updates
CREATE TRIGGER update_proposal_templates_updated_at
  BEFORE UPDATE ON public.proposal_templates
  FOR EACH ROW
  EXECUTE FUNCTION public.update_proposal_templates_updated_at_column();