
-- Create a new public bucket for proposal template images
INSERT INTO storage.buckets (id, name, public)
VALUES ('template_images', 'template_images', true);

-- Allow authenticated users to upload files to the new bucket
CREATE POLICY "Allow authenticated uploads to template_images"
ON storage.objects FOR INSERT
TO authenticated
WITH CHECK (bucket_id = 'template_images');

-- Allow public read access to files in the bucket
CREATE POLICY "Allow public reads from template_images"
ON storage.objects FOR SELECT
TO public
USING (bucket_id = 'template_images');

-- Allow authenticated users to update their own images
CREATE POLICY "Allow authenticated updates for template_images"
ON storage.objects FOR UPDATE
TO authenticated
USING (bucket_id = 'template_images');

-- Allow authenticated users to delete their own images
CREATE POLICY "Allow authenticated deletes for template_images"
ON storage.objects FOR DELETE
TO authenticated
USING (bucket_id = 'template_images');
