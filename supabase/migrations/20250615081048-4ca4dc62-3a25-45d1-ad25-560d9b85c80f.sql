
-- Create a table for categories
CREATE TABLE public.categories (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL UNIQUE,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create a table for specialties
CREATE TABLE public.specialties (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL UNIQUE,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable Row Level Security for both tables
ALTER TABLE public.categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.specialties ENABLE ROW LEVEL SECURITY;

-- Allow public read access to categories and specialties
CREATE POLICY "Public can read categories" ON public.categories FOR SELECT USING (true);
CREATE POLICY "Public can read specialties" ON public.specialties FOR SELECT USING (true);

-- Allow authenticated users to manage categories and specialties
CREATE POLICY "Authenticated users can insert categories" ON public.categories FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Authenticated users can update categories" ON public.categories FOR UPDATE USING (auth.role() = 'authenticated');
CREATE POLICY "Authenticated users can delete categories" ON public.categories FOR DELETE USING (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can insert specialties" ON public.specialties FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Authenticated users can update specialties" ON public.specialties FOR UPDATE USING (auth.role() = 'authenticated');
CREATE POLICY "Authenticated users can delete specialties" ON public.specialties FOR DELETE USING (auth.role() = 'authenticated');

-- Populate tables with existing distinct values from the speakers table and some default values.
INSERT INTO public.categories (name)
SELECT DISTINCT category FROM public.speakers WHERE category IS NOT NULL AND category <> ''
ON CONFLICT (name) DO NOTHING;

INSERT INTO public.categories (name) VALUES
('Technology'), ('Business'), ('Healthcare'), ('Motivation'), ('Environment'), ('Education'), ('Finance'), ('Marketing')
ON CONFLICT (name) DO NOTHING;

INSERT INTO public.specialties (name)
SELECT DISTINCT unnest(specialties) FROM public.speakers WHERE specialties IS NOT NULL
ON CONFLICT (name) DO NOTHING;
