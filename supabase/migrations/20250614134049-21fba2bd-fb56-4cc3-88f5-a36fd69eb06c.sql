
-- Make the 'proposals' bucket private
UPDATE storage.buckets
SET public = false
WHERE id = 'proposals';

-- Drop the old, insecure policies on the storage objects
DROP POLICY IF EXISTS "Public read access for proposals" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can manage proposal files" ON storage.objects;

-- Grant authenticated users specific permissions to manage files in the 'proposals' bucket
CREATE POLICY "Authenticated users can view proposal files"
ON storage.objects FOR SELECT
TO authenticated
USING ( bucket_id = 'proposals' );

CREATE POLICY "Authenticated users can upload proposal files"
ON storage.objects FOR INSERT
TO authenticated
WITH CHECK ( bucket_id = 'proposals' );

CREATE POLICY "Authenticated users can update proposal files"
ON storage.objects FOR UPDATE
TO authenticated
USING ( bucket_id = 'proposals' );

CREATE POLICY "Authenticated users can delete proposal files"
ON storage.objects FOR DELETE
TO authenticated
USING ( bucket_id = 'proposals' );

-- Rename the pdf_url column to pdf_path to better reflect its content
ALTER TABLE public.proposals
RENAME COLUMN pdf_url TO pdf_path;
