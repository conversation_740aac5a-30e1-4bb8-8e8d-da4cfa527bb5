
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import Layout from "../components/Layout";
import { employees as allEmployees } from "../data/employees";
import { getEvents } from "../utils/eventStorage";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Mail, Phone, Briefcase, Calendar } from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";

const EmployeeDetail = () => {
  const { id } = useParams<{ id: string }>();
  const employee = allEmployees.find((emp) => emp.id === id);
  const allEvents = getEvents();
  const assignedEvents = allEvents.filter(
    (event) => event.responsiblePersonId === id
  );

  if (!employee) {
    return (
      <Layout>
        <div className="text-center py-10">
          <h1 className="text-2xl font-bold">Employee not found</h1>
          <p className="text-muted-foreground mt-2">
            The employee you are looking for does not exist.
          </p>
          <Link
            to="/employees"
            className="mt-4 inline-block text-primary hover:underline"
          >
            Back to Employees
          </Link>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <div className="flex items-center gap-4">
              <Avatar className="h-20 w-20">
                <AvatarImage src={employee.image} alt={employee.name} />
                <AvatarFallback>
                  {employee.name
                    .split(" ")
                    .map((n) => n[0])
                    .join("")}
                </AvatarFallback>
              </Avatar>
              <div>
                <h1 className="text-3xl font-bold">{employee.name}</h1>
                <p className="text-muted-foreground flex items-center gap-2 mt-1">
                  <Briefcase className="h-4 w-4" />
                  {employee.role}
                </p>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid gap-2 md:grid-cols-2">
              <a
                href={`mailto:${employee.email}`}
                className="flex items-center gap-2 text-sm text-foreground hover:underline"
              >
                <Mail className="h-4 w-4 text-muted-foreground" />
                {employee.email}
              </a>
              <a
                href={`tel:${employee.phone}`}
                className="flex items-center gap-2 text-sm text-foreground hover:underline"
              >
                <Phone className="h-4 w-4 text-muted-foreground" />
                {employee.phone}
              </a>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Assigned Events</CardTitle>
          </CardHeader>
          <CardContent>
            {assignedEvents.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <Calendar className="h-10 w-10 mx-auto mb-2" />
                No events assigned to this employee.
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Event</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Location</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {assignedEvents.map((event) => (
                    <TableRow key={event.id}>
                      <TableCell className="font-medium">
                        <Link
                          to={`/event/${event.id}`}
                          className="hover:underline"
                        >
                          {event.title}
                        </Link>
                      </TableCell>
                      <TableCell>
                        {new Date(event.date).toLocaleDateString()}
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant={
                            event.status === "Completed"
                              ? "default"
                              : event.status === "Cancelled"
                              ? "destructive"
                              : "secondary"
                          }
                        >
                          {event.status}
                        </Badge>
                      </TableCell>
                      <TableCell>{event.location}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>
      </div>
    </Layout>
  );
};

export default EmployeeDetail;
