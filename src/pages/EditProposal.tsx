import { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import Layout from "@/components/Layout";
import { ProposalHeader } from "@/components/proposals/ProposalHeader";
import { ClientInformation } from "@/components/proposals/ClientInformation";
import { EventDetails } from "@/components/proposals/EventDetails";
import { SimpleSpeakerSelection } from "@/components/proposals/SimpleSpeakerSelection";
import { ProposalActions } from "@/components/proposals/ProposalActions";
import { TemplateManager } from "@/components/TemplateManager";

interface DatabaseProposal {
  id: string;
  user_id: string;
  event_name: string;
  status: string;
  details: any;
  speaker_id: string;
  created_at: string;
  updated_at: string;
  pdf_path: string | null;
  submitted_date: string | null;
}

export default function EditProposal() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [showTemplateManager, setShowTemplateManager] = useState(false);

  const [clientInfo, setClientInfo] = useState({
    name: "",
    email: "",
    company: "",
    phone: "",
    notes: "",
  });

  const [eventInfo, setEventInfo] = useState({
    name: "",
    date: "",
    time: "",
    duration: "",
    location: "",
    description: "",
    audience_size: "",
    event_type: "",
    budget_range: "",
    special_requirements: "",
  });

  const [selectedSpeakers, setSelectedSpeakers] = useState<string[]>([]);

  // Fetch proposal data
  const { data: proposal, isLoading: isLoadingProposal } = useQuery({
    queryKey: ["proposal", id],
    queryFn: async () => {
      if (!id) throw new Error("Proposal ID is required");

      const { data, error } = await supabase
        .from("proposals")
        .select("*")
        .eq("id", id)
        .single();

      if (error) throw error;
      return data as DatabaseProposal;
    },
    enabled: !!id,
  });

  // Fetch speakers
  const { data: speakers = [] } = useQuery({
    queryKey: ["speakers"],
    queryFn: async () => {
      const { data, error } = await supabase.from("speakers").select("*");
      if (error) throw error;
      return data || [];
    },
  });

  // Populate form when proposal data is loaded
  useEffect(() => {
    if (proposal) {
      const details = proposal.details || {};

      setClientInfo({
        name: details.client_name || "",
        email: details.client_email || "",
        company: details.client_company || "",
        phone: details.client_phone || "",
        notes: details.client_notes || "",
      });

      setEventInfo({
        name: proposal.event_name || "",
        date: details.event_date || "",
        time: details.event_time || "",
        duration: details.event_duration || "",
        location: details.event_location || "",
        description: details.event_description || "",
        audience_size: details.audience_size || "",
        event_type: details.event_type || "",
        budget_range: details.budget_range || "",
        special_requirements: details.special_requirements || "",
      });

      setSelectedSpeakers(details.speakers || []);
    }
  }, [proposal]);

  // Update proposal mutation
  const updateProposalMutation = useMutation({
    mutationFn: async () => {
      if (!id) throw new Error("Proposal ID is required");

      // Validate required fields
      if (
        !clientInfo.name ||
        !clientInfo.email ||
        !eventInfo.name ||
        !eventInfo.date
      ) {
        throw new Error("Please fill in all required fields");
      }

      const proposalData = {
        event_name: eventInfo.name,
        details: {
          client_name: clientInfo.name,
          client_email: clientInfo.email,
          client_company: clientInfo.company,
          client_phone: clientInfo.phone,
          client_notes: clientInfo.notes,
          event_date: eventInfo.date,
          event_time: eventInfo.time,
          event_duration: eventInfo.duration,
          event_location: eventInfo.location,
          event_description: eventInfo.description,
          audience_size: eventInfo.audience_size,
          event_type: eventInfo.event_type,
          budget_range: eventInfo.budget_range,
          special_requirements: eventInfo.special_requirements,
          speakers: selectedSpeakers,
          total_budget: eventInfo.budget_range,
        },
        speaker_id: selectedSpeakers[0] || null,
      };

      const { data, error } = await supabase
        .from("proposals")
        .update(proposalData)
        .eq("id", id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      toast({
        title: "Proposal Updated",
        description: "Your proposal has been updated successfully.",
      });
      queryClient.invalidateQueries({ queryKey: ["proposals"] });
      queryClient.invalidateQueries({ queryKey: ["proposal", id] });
      navigate("/proposals");
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to update proposal",
        variant: "destructive",
      });
    },
  });

  const handleSave = () => {
    updateProposalMutation.mutate();
  };

  const handleBack = () => {
    navigate("/proposals");
  };

  const handleEditTemplate = () => {
    setShowTemplateManager(true);
  };

  if (isLoadingProposal) {
    return (
      <Layout>
        <div className="flex justify-center items-center h-64">
          <div className="text-lg">Loading proposal...</div>
        </div>
      </Layout>
    );
  }

  if (!proposal) {
    return (
      <Layout>
        <div className="flex justify-center items-center h-64">
          <div className="text-lg">Proposal not found</div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container mx-auto px-6 py-8 space-y-8">
        <ProposalHeader
          isEditing={true}
          onBack={handleBack}
          onEditTemplate={handleEditTemplate}
        />

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div className="space-y-6">
            <ClientInformation
              clientInfo={clientInfo}
              setClientInfo={setClientInfo}
            />

            <EventDetails
              eventDetails={eventInfo}
              setEventDetails={setEventInfo}
            />
          </div>

          <div className="space-y-6">
            <SimpleSpeakerSelection
              speakers={speakers}
              selectedSpeakers={selectedSpeakers}
              setSelectedSpeakers={setSelectedSpeakers}
            />

            <ProposalActions
              onSave={handleSave}
              onCancel={handleBack}
              isEditing={true}
              isLoading={updateProposalMutation.isPending}
            />
          </div>
        </div>

        {showTemplateManager && (
          <TemplateManager
            open={showTemplateManager}
            onOpenChange={setShowTemplateManager}
            mode="manage"
          />
        )}
      </div>
    </Layout>
  );
}
