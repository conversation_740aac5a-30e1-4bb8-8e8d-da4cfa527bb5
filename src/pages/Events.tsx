import { useState, useEffect, useCallback } from "react";
import Layout from "../components/Layout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import {
  Calendar,
  Plus,
  Edit,
  Trash2,
  MapPin,
  Clock,
  Users,
  Filter,
  X,
  UserCog,
  Download,
  Loader2
} from "lucide-react";
import { Event } from "../types/event";
import { useToast } from "@/hooks/use-toast";
import { Checkbox } from "@/components/ui/checkbox";
import { useLocation, useNavigate } from "react-router-dom";
import EventStats from "../components/EventStats";
import { exportToCsv } from "../utils/csvExporter";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { Speaker } from "@/types/speaker";
import { employees as allEmployees } from "../data/employees"; // Keep using local data for now

const Events = () => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingEvent, setEditingEvent] = useState<Event | null>(null);
  const { toast } = useToast();
  const location = useLocation();
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  // Filter states
  const [speakerFilter, setSpeakerFilter] = useState<string>("all");
  const [locationFilter, setLocationFilter] = useState<string>("all-locations");
  const [dateFilter, setDateFilter] = useState<string>("");

  const [formData, setFormData] = useState<Omit<Event, 'id' | 'createdAt'>>({
    title: "",
    date: "",
    startTime: "",
    endTime: "",
    location: "",
    description: "",
    speakerIds: [],
    responsiblePersonId: "",
    status: "Scheduled",
  });
  
  const { data: events = [], isLoading: isLoadingEvents } = useQuery<Event[]>({
    queryKey: ['events'],
    queryFn: async () => {
      const { data, error } = await supabase.from('events').select('*').order('date', { ascending: false });
      if (error) throw new Error(error.message);
      if (!data) return [];
      // Map snake_case from DB to camelCase for the app and handle nulls
      return data.map(e => ({
        id: e.id,
        title: e.title,
        date: e.date,
        startTime: e.startTime || "",
        endTime: e.endTime || "",
        location: e.location || "",
        description: e.description || "",
        speakerIds: e.speakerIds || [],
        status: (e.status as Event['status']) || 'Scheduled',
        createdAt: e.created_at,
        responsiblePersonId: e.responsible_person_id || undefined,
      }));
    }
  });

  const { data: speakers = [] } = useQuery<Pick<Speaker, 'id' | 'name'>[]>({
    queryKey: ['speakers-slim'],
    queryFn: async () => {
      const { data, error } = await supabase.from('speakers').select('id, name');
      if (error) throw new Error(error.message);
      return data || [];
    }
  });

  const handleEdit = useCallback((event: Event) => {
    setEditingEvent(event);
    setFormData({
      title: event.title || "",
      date: event.date ? new Date(event.date).toISOString().split("T")[0] : "",
      startTime: event.startTime || "",
      endTime: event.endTime || "",
      location: event.location || "",
      description: event.description || "",
      speakerIds: event.speakerIds || [],
      responsiblePersonId: event.responsiblePersonId || "",
      status: event.status || "Scheduled",
    });
    setIsDialogOpen(true);
  }, []);

  const handleDownload = () => {
    if (events.length === 0) {
      toast({
        title: "No Events to Export",
        description: "There are no events to download.",
        variant: "destructive",
      });
      return;
    }

    const eventsForExport = events.map(event => {
      const { speakerIds, responsiblePersonId, ...rest } = event;
      return {
        ...rest,
        speakers: (speakerIds || [])
          .map(id => speakers.find(s => s.id === id)?.name)
          .filter(Boolean)
          .join('; '),
        responsiblePerson: allEmployees.find(e => e.id === responsiblePersonId)?.name || ''
      };
    });

    exportToCsv({
      data: eventsForExport,
      filename: "events_backup",
    });
    toast({
      title: "Download Started",
      description: "Your events data is being downloaded.",
    });
  };

  const handleDialogChange = (open: boolean) => {
    setIsDialogOpen(open);
    if (!open) {
      resetForm();
    }
  };

  useEffect(() => {
    if (isLoadingEvents) return;
    const params = new URLSearchParams(location.search);
    const eventIdToEdit = params.get("edit");
    if (eventIdToEdit) {
      const eventToEdit = events.find((e) => e.id === eventIdToEdit);
      if (eventToEdit) {
        handleEdit(eventToEdit);
        navigate("/events", { replace: true });
      }
    }
  }, [location.search, handleEdit, navigate, events, isLoadingEvents]);

  // Get unique locations from events
  const uniqueLocations = [
    ...new Set(events.map((event) => event.location).filter(Boolean) as string[]),
  ];

  // Filter events based on selected filters
  const filteredEvents = events.filter((event) => {
    const matchesSpeaker =
      speakerFilter === "all" ||
      (event.speakerIds && event.speakerIds.includes(speakerFilter));

    const matchesLocation =
      locationFilter === "all-locations" ||
      (event.location && event.location.toLowerCase().includes(locationFilter.toLowerCase()));

    const matchesDate =
      !dateFilter ||
      (event.date && new Date(event.date).toISOString().split("T")[0] === dateFilter);

    return matchesSpeaker && matchesLocation && matchesDate;
  });

  const resetFilters = () => {
    setSpeakerFilter("all");
    setLocationFilter("all-locations");
    setDateFilter("");
  };

  const resetForm = () => {
    setFormData({
      title: "",
      date: "",
      startTime: "",
      endTime: "",
      location: "",
      description: "",
      speakerIds: [],
      responsiblePersonId: "",
      status: "Scheduled",
    });
    setEditingEvent(null);
  };
  
  const eventMutation = useMutation({
    mutationFn: async (eventData: Omit<Event, 'id' | 'createdAt'> | Event) => {
      const { responsiblePersonId, ...restOfData } = eventData as Event;
      
      const dataForSupabase = {
        ...restOfData,
        responsible_person_id: responsiblePersonId,
      };

      if (editingEvent) {
        const { id, createdAt, ...updateData } = dataForSupabase;
        const { error } = await supabase.from('events').update(updateData).eq('id', editingEvent.id);
        if (error) throw error;
      } else {
        const { id, createdAt, ...insertData } = dataForSupabase;
        const { error } = await supabase.from('events').insert([insertData]);
        if (error) throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['events'] });
      setIsDialogOpen(false);
      resetForm();
      toast({
        title: editingEvent ? "Event Updated" : "Event Created",
        description: `Event has been ${editingEvent ? "updated" : "created"} successfully.`,
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    }
  });

  const deleteEventMutation = useMutation({
    mutationFn: async (eventId: string) => {
      const { error } = await supabase.from('events').delete().eq('id', eventId);
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['events'] });
      toast({
        title: "Event Deleted",
        description: "The event has been deleted successfully.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error Deleting Event",
        description: error.message,
        variant: "destructive",
      });
    }
  });


  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.title || !formData.date || !formData.startTime || !formData.endTime) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields.",
        variant: "destructive",
      });
      return;
    }
    
    eventMutation.mutate({
      ...formData,
      responsiblePersonId: formData.responsiblePersonId === 'none' ? null : formData.responsiblePersonId,
    });
  };

  const handleDelete = (eventId: string) => {
    deleteEventMutation.mutate(eventId);
  };

  const handleSpeakerChange = (speakerId: string, checked: boolean) => {
    if (checked) {
      setFormData((prev) => ({
        ...prev,
        speakerIds: [...(prev.speakerIds || []), speakerId],
      }));
    } else {
      setFormData((prev) => ({
        ...prev,
        speakerIds: (prev.speakerIds || []).filter((id) => id !== speakerId),
      }));
    }
  };

  const getStatusColor = (status: Event["status"]) => {
    switch (status) {
      case "Scheduled":
        return "bg-blue-100 text-blue-800";
      case "Completed":
        return "bg-green-100 text-green-800";
      case "Cancelled":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <Layout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-foreground">Events</h1>
            <p className="text-muted-foreground mt-2">
              Manage and schedule your speaking events
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" onClick={handleDownload}>
              <Download className="mr-2 h-4 w-4" />
              Download
            </Button>
            <Dialog open={isDialogOpen} onOpenChange={handleDialogChange}>
              <DialogTrigger asChild>
                <Button onClick={() => {
                  resetForm();
                  setIsDialogOpen(true);
                }}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Event
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle>
                    {editingEvent ? "Edit Event" : "Create New Event"}
                  </DialogTitle>
                </DialogHeader>
                <form onSubmit={handleSubmit} className="space-y-4 py-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="title">Event Title *</Label>
                      <Input
                        id="title"
                        value={formData.title}
                        onChange={(e) =>
                          setFormData((prev) => ({
                            ...prev,
                            title: e.target.value,
                          }))
                        }
                        placeholder="Enter event title"
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="status">Status</Label>
                      <Select
                        value={formData.status || 'Scheduled'}
                        onValueChange={(value: Event["status"]) =>
                          setFormData((prev) => ({ ...prev, status: value }))
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Scheduled">Scheduled</SelectItem>
                          <SelectItem value="Completed">Completed</SelectItem>
                          <SelectItem value="Cancelled">Cancelled</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="grid grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="date">Date *</Label>
                      <Input
                        id="date"
                        type="date"
                        value={formData.date}
                        onChange={(e) =>
                          setFormData((prev) => ({
                            ...prev,
                            date: e.target.value,
                          }))
                        }
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="startTime">Start Time *</Label>
                      <Input
                        id="startTime"
                        type="time"
                        value={formData.startTime}
                        onChange={(e) =>
                          setFormData((prev) => ({
                            ...prev,
                            startTime: e.target.value,
                          }))
                        }
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="endTime">End Time *</Label>
                      <Input
                        id="endTime"
                        type="time"
                        value={formData.endTime}
                        onChange={(e) =>
                          setFormData((prev) => ({
                            ...prev,
                            endTime: e.target.value,
                          }))
                        }
                        required
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="location">Location</Label>
                      <Input
                        id="location"
                        value={formData.location || ''}
                        onChange={(e) =>
                          setFormData((prev) => ({
                            ...prev,
                            location: e.target.value,
                          }))
                        }
                        placeholder="Enter event location"
                      />
                    </div>
                     <div className="space-y-2">
                      <Label htmlFor="responsiblePerson">Responsible Person</Label>
                      <Select
                        value={formData.responsiblePersonId || ''}
                        onValueChange={(value) =>
                          setFormData((prev) => ({ ...prev, responsiblePersonId: value }))
                        }
                      >
                        <SelectTrigger id="responsiblePerson">
                          <SelectValue placeholder="Select a person" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="none">None</SelectItem>
                          {allEmployees.map((employee) => (
                            <SelectItem key={employee.id} value={employee.id}>
                              {employee.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="description">Description</Label>
                    <Textarea
                      id="description"
                      value={formData.description || ''}
                      onChange={(e) =>
                        setFormData((prev) => ({
                          ...prev,
                          description: e.target.value,
                        }))
                      }
                      placeholder="Enter event description"
                      rows={3}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>Assign Speakers</Label>
                    <div className="grid grid-cols-2 gap-2 max-h-40 overflow-y-auto border rounded-lg p-3">
                      {speakers.map((speaker) => (
                        <div
                          key={speaker.id}
                          className="flex items-center space-x-2"
                        >
                          <Checkbox
                            id={`speaker-${speaker.id}`}
                            checked={(formData.speakerIds || []).includes(speaker.id)}
                            onCheckedChange={(checked) =>
                              handleSpeakerChange(speaker.id, checked as boolean)
                            }
                          />
                          <Label
                            htmlFor={`speaker-${speaker.id}`}
                            className="text-sm cursor-pointer"
                          >
                            {speaker.name}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="flex justify-end space-x-2 pt-4">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setIsDialogOpen(false)}
                    >
                      Cancel
                    </Button>
                    <Button type="submit" disabled={eventMutation.isPending}>
                      {eventMutation.isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                      {editingEvent ? "Update Event" : "Create Event"}
                    </Button>
                  </div>
                </form>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        <EventStats events={events} />

        {/* Filter section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Filter className="h-5 w-5 mr-2" />
              Filters
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="speakerFilter">Speaker</Label>
                <Select value={speakerFilter} onValueChange={setSpeakerFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Filter by speaker" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Speakers</SelectItem>
                    {speakers.map((speaker) => (
                      <SelectItem key={speaker.id} value={speaker.id || "all"}>
                        {speaker.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="locationFilter">Location</Label>
                <Select
                  value={locationFilter}
                  onValueChange={setLocationFilter}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Filter by location" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all-locations">All Locations</SelectItem>
                    {uniqueLocations.map((location) => (
                      <SelectItem key={location} value={location}>
                        {location}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="dateFilter">Date</Label>
                <div className="flex gap-2">
                  <Input
                    id="dateFilter"
                    type="date"
                    value={dateFilter}
                    onChange={(e) => setDateFilter(e.target.value)}
                  />
                  {(speakerFilter !== "all" ||
                    locationFilter !== "all-locations" ||
                    dateFilter) && (
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={resetFilters}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="grid gap-6">
          {isLoadingEvents ? (
            <div className="text-center py-8">
              <Loader2 className="h-12 w-12 text-muted-foreground mx-auto mb-4 animate-spin" />
              <p className="text-muted-foreground">Loading events...</p>
            </div>
          ) : filteredEvents.length === 0 ? (
            <Card>
              <CardContent className="text-center py-8">
                <Calendar className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">
                  {events.length === 0
                    ? 'No events created yet. Click "Add Event" to get started.'
                    : "No events match your filters."}
                </p>
              </CardContent>
            </Card>
          ) : (
            filteredEvents.map((event) => (
              <Card key={event.id}>
                <CardHeader className="flex flex-row items-start justify-between">
                  <div>
                    <CardTitle
                      className="text-xl cursor-pointer hover:text-primary transition-colors"
                      onClick={() => navigate(`/event/${event.id}`)}
                    >
                      {event.title}
                    </CardTitle>
                    <div className="flex flex-wrap items-center gap-x-4 gap-y-2 mt-2 text-sm text-muted-foreground">
                      {event.date && (
                        <div className="flex items-center space-x-1">
                          <Calendar className="h-4 w-4" />
                          <span>{new Date(event.date).toDateString()}</span>
                        </div>
                      )}
                      {event.startTime && event.endTime && (
                        <div className="flex items-center space-x-1">
                          <Clock className="h-4 w-4" />
                          <span>
                            {event.startTime} - {event.endTime}
                          </span>
                        </div>
                      )}
                      {event.location && (
                        <div className="flex items-center space-x-1">
                          <MapPin className="h-4 w-4" />
                          <span>{event.location}</span>
                        </div>
                        )}
                      {event.responsiblePersonId && (
                        <div className="flex items-center space-x-1">
                          <UserCog className="h-4 w-4" />
                          <span>
                            {allEmployees.find(e => e.id === event.responsiblePersonId)?.name || 'N/A'}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center space-x-2 flex-shrink-0">
                    <Badge className={getStatusColor(event.status)}>
                      {event.status}
                    </Badge>
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => handleEdit(event)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="destructive"
                      size="icon"
                      onClick={() => handleDelete(event.id)}
                      disabled={deleteEventMutation.isPending && deleteEventMutation.variables === event.id}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  {event.description && (
                    <p className="text-muted-foreground mb-4">
                      {event.description}
                    </p>
                  )}
                  {event.speakerIds && event.speakerIds.length > 0 && (
                    <div>
                      <div className="flex items-center space-x-2 mb-2">
                        <Users className="h-4 w-4" />
                        <span className="font-medium">Speakers:</span>
                      </div>
                      <div className="flex flex-wrap gap-2">
                        {event.speakerIds.map((speakerId) => {
                          const speaker = speakers.find(
                            (s) => s.id === speakerId
                          );
                          return speaker ? (
                            <Badge key={speakerId} variant="secondary">
                              {speaker.name}
                            </Badge>
                          ) : null;
                        })}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))
          )}
        </div>
      </div>
    </Layout>
  );
};

export default Events;
