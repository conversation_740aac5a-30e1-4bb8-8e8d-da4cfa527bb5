import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Plus, Edit, FileText, Download, Eye, Trash2 } from "lucide-react";
import { format } from "date-fns";
import { generateProposalPDF } from "@/utils/pdfGenerator";
import { generateProposalPPTX } from "@/utils/pptxGenerator";
import { Proposal, ProposalStatus } from "@/types/proposal";
import Layout from "@/components/Layout";

interface DatabaseProposal {
  id: string;
  event_name: string;
  status: string;
  details: any;
  speaker_id: string;
  created_at: string;
  pdf_path: string | null;
  submitted_date: string | null;
}

export default function Proposals() {
  const navigate = useNavigate();
  const { toast } = useToast();

  const [isGenerating, setIsGenerating] = useState(false);

  const fetchProposals = async (): Promise<DatabaseProposal[]> => {
    const {
      data: { user },
    } = await supabase.auth.getUser();
    if (!user) return [];

    const { data, error } = await supabase
      .from("proposals")
      .select("*")
      .order("created_at", { ascending: false });

    if (error) throw error;
    return data || [];
  };

  const {
    data: proposals,
    isLoading,
    refetch,
  } = useQuery({
    queryKey: ["proposals"],
    queryFn: fetchProposals,
  });

  useEffect(() => {
    refetch();
  }, [refetch]);

  // Transform DatabaseProposal to Proposal interface
  const transformProposal = async (
    dbProposal: DatabaseProposal
  ): Promise<Proposal> => {
    const details = dbProposal.details || {};

    // Fetch speaker details if we have speaker IDs
    let speakers = [];
    if (
      details.speakers &&
      Array.isArray(details.speakers) &&
      details.speakers.length > 0
    ) {
      try {
        const { data: speakerData, error } = await supabase
          .from("speakers")
          .select("*")
          .in("id", details.speakers);

        if (!error && speakerData) {
          speakers = speakerData.map((speaker) => ({
            speaker: {
              id: speaker.id,
              name: speaker.name,
              bio: speaker.bio || "",
              category: speaker.category || "",
              image:
                speaker.image ||
                `https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=400&fit=crop&crop=face`,
              rate: speaker.rate || 0,
              location: speaker.location || "",
              experience: speaker.experience || "",
              specialties: speaker.specialties || [],
              availability: speaker.availability || "Available",
            },
            role: "Speaker",
            notes: "",
          }));
        }
      } catch (error) {
        console.error("Error fetching speaker details:", error);
      }
    }

    return {
      id: dbProposal.id,
      clientName: details.client_name || "",
      clientEmail: details.client_email || "",
      event: {
        eventName: dbProposal.event_name || details.event_name || "",
        eventDate: details.event_date || "",
        eventLocation: details.event_location || "",
        eventType: details.event_type || "",
        audience: details.audience_size || "",
        budget: details.budget_range || "",
        description: details.event_description || "",
      },
      speakers: speakers,
      totalBudget: parseFloat(
        details.total_budget || details.budget_range || "0"
      ),
      createdAt: dbProposal.created_at,
      pdfPath: dbProposal.pdf_path || undefined,
      status: (dbProposal.status as ProposalStatus) || "Draft",
    };
  };

  const handleGeneratePDF = async (proposal: DatabaseProposal) => {
    setIsGenerating(true);
    try {
      const transformedProposal = await transformProposal(proposal);
      await generateProposalPDF(transformedProposal, null);
      toast({
        title: "PDF Generated",
        description:
          "Proposal PDF has been generated and downloaded successfully.",
      });
    } catch (error: any) {
      console.error("PDF generation error:", error);
      toast({
        title: "Error generating PDF",
        description: error.message || "Failed to generate PDF",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const handleGeneratePPTX = async (proposal: DatabaseProposal) => {
    setIsGenerating(true);
    try {
      const transformedProposal = await transformProposal(proposal);
      await generateProposalPPTX(transformedProposal);
      toast({
        title: "PPTX Generated",
        description:
          "Proposal PPTX has been generated and downloaded successfully.",
      });
    } catch (error: any) {
      console.error("PPTX generation error:", error);
      toast({
        title: "Error generating PPTX",
        description: error.message || "Failed to generate PPTX",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const handleDelete = async (id: string) => {
    if (window.confirm("Are you sure you want to delete this proposal?")) {
      try {
        // Get the proposal to check if it has a PDF file
        const proposalToDelete = proposals?.find((p) => p.id === id);
        if (proposalToDelete?.pdf_path) {
          try {
            const path = proposalToDelete.pdf_path;
            if (path) {
              await supabase.storage.from("proposals").remove([path]);
            }
          } catch (e) {
            console.error("Could not delete PDF from storage", e);
          }
        }

        const { error } = await supabase
          .from("proposals")
          .delete()
          .eq("id", id);

        if (error) {
          toast({
            title: "Error Deleting",
            description: "Could not delete proposal.",
            variant: "destructive",
          });
        } else {
          toast({
            title: "Proposal Deleted",
            description: "The proposal has been successfully deleted.",
          });
          // Refetch proposals to update the UI
          refetch();
        }
      } catch (error) {
        console.error("Delete error:", error);
        toast({
          title: "Error Deleting",
          description:
            "An unexpected error occurred while deleting the proposal.",
          variant: "destructive",
        });
      }
    }
  };

  if (isLoading) {
    return (
      <Layout>
        <div className="flex justify-center items-center h-64">
          <div className="text-lg">Loading proposals...</div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container mx-auto px-6 py-8">
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-foreground">Proposals</h1>
            <p className="text-muted-foreground mt-2">
              Manage and track your speaker proposals
            </p>
          </div>
          <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={() => navigate("/edit-template")}
            >
              <Edit className="w-4 h-4 mr-2" />
              Edit Template
            </Button>
            <Button onClick={() => navigate("/create-proposal")}>
              <Plus className="w-4 h-4 mr-2" />
              Create New Proposal
            </Button>
          </div>
        </div>

        {proposals?.length === 0 ? (
          <div className="text-center py-12">
            <FileText className="w-16 h-16 mx-auto text-muted-foreground mb-4" />
            <h2 className="text-2xl font-semibold mb-2">No proposals yet</h2>
            <p className="text-muted-foreground mb-4">
              Create your first proposal to get started
            </p>
            <Button onClick={() => navigate("/create-proposal")}>
              <Plus className="w-4 h-4 mr-2" />
              Create New Proposal
            </Button>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {proposals?.map((proposal) => {
              const details = proposal.details as {
                client_name?: string;
                event_date?: string;
                event_location?: string;
                speakers?: string[];
                total_budget?: string;
              };

              let statusColor:
                | "default"
                | "secondary"
                | "destructive"
                | "outline" = "secondary";
              switch (proposal.status) {
                case "draft":
                  statusColor = "secondary";
                  break;
                case "submitted":
                  statusColor = "default";
                  break;
                case "accepted":
                  statusColor = "default";
                  break;
                case "rejected":
                  statusColor = "destructive";
                  break;
                default:
                  statusColor = "secondary";
                  break;
              }

              return (
                <Card
                  key={proposal.id}
                  className="hover:shadow-lg transition-shadow"
                >
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <CardTitle className="text-lg font-semibold line-clamp-1">
                          {proposal.event_name}
                        </CardTitle>
                        <p className="text-sm text-muted-foreground mt-1">
                          Client: {details?.client_name || "N/A"}
                        </p>
                      </div>
                      <Badge variant={statusColor}>{proposal.status}</Badge>
                    </div>
                  </CardHeader>

                  <CardContent className="space-y-4">
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">
                          Event Date:
                        </span>
                        <span>{details?.event_date || "TBD"}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Location:</span>
                        <span className="truncate ml-2">
                          {details?.event_location || "TBD"}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Created:</span>
                        <span>
                          {format(
                            new Date(proposal.created_at),
                            "MMM dd, yyyy"
                          )}
                        </span>
                      </div>
                    </div>

                    <div className="pt-2 border-t">
                      <div className="flex justify-between items-center mb-3">
                        <span className="text-sm font-medium">
                          Speakers: {details?.speakers?.length || 0}
                        </span>
                        <span className="text-sm text-muted-foreground">
                          Budget: {details?.total_budget || "TBD"}
                        </span>
                      </div>

                      <div className="flex gap-2 mb-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() =>
                            navigate(`/proposals/${proposal.id}/edit`)
                          }
                          className="flex-1"
                        >
                          <Edit className="w-3 h-3 mr-1" />
                          Edit
                        </Button>
                        <Button
                          size="sm"
                          variant="destructive"
                          onClick={() => handleDelete(proposal.id)}
                          className="flex-1"
                        >
                          <Trash2 className="w-3 h-3 mr-1" />
                          Delete
                        </Button>
                      </div>
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleGeneratePDF(proposal)}
                          disabled={isGenerating}
                          className="flex-1"
                        >
                          <FileText className="w-3 h-3 mr-1" />
                          PDF
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleGeneratePPTX(proposal)}
                          disabled={isGenerating}
                          className="flex-1"
                        >
                          <FileText className="w-3 h-3 mr-1" />
                          PPTX
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        )}
      </div>
    </Layout>
  );
}
