
import { useAuth } from "@/contexts/AuthContext";
import Layout from "@/components/Layout";
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { useProfile } from "@/hooks/useProfile";
import { useQueryClient } from "@tanstack/react-query";
import { useState } from "react";
import { Loader2 } from "lucide-react";

const Profile = () => {
  const { user } = useAuth();
  const { data: profile } = useProfile();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isUploading, setIsUploading] = useState(false);

  const getInitials = () => {
    if (user?.email) {
      const emailParts = user.email.split('@');
      const name = emailParts[0];
      return name.slice(0, 2).toUpperCase();
    }
    return "ME";
  };
  
  const handleUploadAvatar = async (event: React.ChangeEvent<HTMLInputElement>) => {
    try {
      setIsUploading(true);
      if (!event.target.files || event.target.files.length === 0) {
        throw new Error("You must select an image to upload.");
      }
      if (!user) {
        throw new Error("You must be logged in to upload an avatar.");
      }

      const file = event.target.files[0];
      const fileExt = file.name.split(".").pop();
      const fileName = `${user.id}.${fileExt}`;
      const filePath = `${fileName}`;

      const { error: uploadError } = await supabase.storage
        .from("avatars")
        .upload(filePath, file, { upsert: true });

      if (uploadError) {
        throw uploadError;
      }

      const { data: { publicUrl } } = supabase.storage.from('avatars').getPublicUrl(filePath);

      const { error: updateError } = await supabase
        .from("profiles")
        .update({ avatar_url: publicUrl, updated_at: new Date().toISOString() })
        .eq("id", user.id);

      if (updateError) {
        throw updateError;
      }
      
      toast({
        title: "Avatar updated!",
        description: "Your new avatar has been saved.",
      });
      queryClient.invalidateQueries({ queryKey: ['profile', user.id] });
    } catch (error: any) {
      toast({
        title: "Error uploading avatar",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setIsUploading(false);
    }
  };


  return (
    <Layout>
      <div className="space-y-6">
        <h1 className="text-3xl font-bold tracking-tight text-foreground">Profile</h1>
        <div className="max-w-xl mx-auto">
          <Card>
            <CardHeader>
              <CardTitle>Your Details</CardTitle>
              <CardDescription>Update your personal information and avatar.</CardDescription>
            </CardHeader>
            <CardContent className="flex flex-col items-center space-y-4 pt-6">
              <Avatar className="h-24 w-24">
                <AvatarImage src={profile?.avatar_url || undefined} alt="User Avatar" />
                <AvatarFallback className="text-4xl">{getInitials()}</AvatarFallback>
              </Avatar>
              <div className="text-center">
                <p className="text-lg font-semibold">{user?.email}</p>
                <p className="text-sm text-muted-foreground">Joined on {user?.created_at ? new Date(user.created_at).toLocaleDateString() : 'N/A'}</p>
              </div>
              <div className="w-full pt-2">
                <Button asChild className="w-full cursor-pointer" variant="outline" disabled={isUploading}>
                  <label htmlFor="avatar-upload">
                    {isUploading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
                    {isUploading ? 'Uploading...' : 'Upload Avatar'}
                  </label>
                </Button>
                <Input id="avatar-upload" type="file" className="hidden" accept="image/*" onChange={handleUploadAvatar} disabled={isUploading} />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </Layout>
  );
};

export default Profile;
