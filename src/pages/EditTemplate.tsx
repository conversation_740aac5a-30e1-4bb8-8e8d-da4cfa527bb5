
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import Layout from "@/components/Layout";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { TemplateManager } from "@/components/TemplateManager";

export default function EditTemplate() {
  const navigate = useNavigate();
  const [showTemplateManager, setShowTemplateManager] = useState(true);

  return (
    <Layout>
      <div className="container mx-auto px-6 py-8">
        <div className="flex items-center space-x-4 mb-8">
          <Button variant="outline" onClick={() => navigate('/proposals')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Proposals
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-foreground">Edit Templates</h1>
            <p className="text-muted-foreground mt-2">
              Manage your proposal templates
            </p>
          </div>
        </div>

        <TemplateManager
          open={showTemplateManager}
          onOpenChange={setShowTemplateManager}
          mode="manage"
        />
      </div>
    </Layout>
  );
}
