import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import Layout from "@/components/Layout";
import DashboardStatCard from "@/components/DashboardStatCard";
import SpeakerCategoryChart from "@/components/SpeakerCategoryChart";
import EventStats from "@/components/EventStats";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { 
  Users, 
  Calendar, 
  FileText, 
  TrendingUp, 
  Gift,
  User
} from "lucide-react";
import { format, isToday } from "date-fns";

const Index = () => {
  // Fetch speakers count
  const { data: speakersCount = 0 } = useQuery({
    queryKey: ['speakers-count'],
    queryFn: async () => {
      const { count, error } = await supabase.from('speakers').select('*', { count: 'exact', head: true });
      if (error) throw error;
      return count || 0;
    }
  });

  // Fetch events count
  const { data: eventsCount = 0 } = useQuery({
    queryKey: ['events-count'],
    queryFn: async () => {
      const { count, error } = await supabase.from('events').select('*', { count: 'exact', head: true });
      if (error) throw error;
      return count || 0;
    }
  });

  // Fetch proposals count
  const { data: proposalsCount = 0 } = useQuery({
    queryKey: ['proposals-count'],
    queryFn: async () => {
      const { count, error } = await supabase.from('proposals').select('*', { count: 'exact', head: true });
      if (error) throw error;
      return count || 0;
    }
  });

  // Fetch birthdays this month
  const { data: birthdaysThisMonth = [] } = useQuery({
    queryKey: ['birthdays-this-month'],
    queryFn: async () => {
      const today = new Date();
      const thisMonth = today.getMonth() + 1;
      
      const { data, error } = await supabase
        .from('speakers')
        .select('id, name, date_of_birth')
        .not('date_of_birth', 'is', null)
        .like('date_of_birth', `%-${String(thisMonth).padStart(2, '0')}-%`);

      if (error) throw error;
      return data || [];
    }
  });

  // Fetch today's birthdays
  const { data: todaysBirthdays = [] } = useQuery({
    queryKey: ['todays-birthdays'],
    queryFn: async () => {
      const today = new Date();
      const monthDay = `${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;
      
      const { data, error } = await supabase
        .from('speakers')
        .select('id, name, date_of_birth')
        .not('date_of_birth', 'is', null)
        .like('date_of_birth', `%-${monthDay}`);

      if (error) throw error;
      return data || [];
    }
  });

  // Fetch recent events
  const { data: recentEvents = [] } = useQuery({
    queryKey: ['recent-events'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('events')
        .select('*')
        .order('date', { ascending: true })
        .limit(5);

      if (error) throw error;
      return data || [];
    }
  });

  // Fetch speaker categories for chart
  const { data: categoryData = [] } = useQuery({
    queryKey: ['speaker-categories'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('speakers')
        .select('category')
        .not('category', 'is', null);
      
      if (error) throw error;
      
      const categoryCounts = data.reduce((acc: any, speaker) => {
        const category = speaker.category || 'Uncategorized';
        acc[category] = (acc[category] || 0) + 1;
        return acc;
      }, {});
      
      return Object.entries(categoryCounts).map(([name, total]) => ({ name, total: total as number }));
    }
  });

  // Transform events data to match Event type with proper status casting
  const transformedEvents = recentEvents.map(event => ({
    id: event.id,
    title: event.title,
    date: event.date,
    location: event.location || '',
    description: event.description || '',
    status: (event.status || 'Scheduled') as 'Scheduled' | 'Completed' | 'Cancelled',
    startTime: event.startTime || '',
    endTime: event.endTime || '',
    speakerIds: event.speakerIds || [],
    responsiblePersonId: event.responsible_person_id,
    createdAt: event.created_at
  }));

  return (
    <Layout>
      <div className="space-y-8">
        {/* Header */}
        <div>
          <h1 className="text-4xl font-bold text-foreground mb-2">Dashboard</h1>
          <p className="text-muted-foreground text-lg">Welcome to your Speaker Agency dashboard</p>
        </div>

        {/* Today's Birthdays Alert */}
        {todaysBirthdays.length > 0 && (
          <Card className="border-pink-200 bg-gradient-to-r from-pink-50 to-purple-50 dark:from-pink-950/30 dark:to-purple-950/30">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-pink-700 dark:text-pink-300">
                <Gift className="h-5 w-5" />
                🎉 Today's Birthdays ({todaysBirthdays.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-3">
                {todaysBirthdays.map((speaker) => (
                  <div key={speaker.id} className="flex items-center space-x-2 bg-white/50 dark:bg-gray-800/50 rounded-full px-3 py-1">
                    <Avatar className="h-6 w-6">
                      <AvatarFallback className="text-xs bg-pink-100 text-pink-700">
                        {speaker.name.charAt(0)}
                      </AvatarFallback>
                    </Avatar>
                    <span className="text-sm font-medium">{speaker.name}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Stats Grid */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          <DashboardStatCard
            title="Total Speakers"
            value={speakersCount}
            icon={Users}
            trend={{ value: 12, isPositive: true }}
            className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/30 border-blue-200 dark:border-blue-800"
          />
          <DashboardStatCard
            title="Active Events"
            value={eventsCount}
            icon={Calendar}
            trend={{ value: 8, isPositive: true }}
            className="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-950/30 dark:to-emerald-950/30 border-green-200 dark:border-green-800"
          />
          <DashboardStatCard
            title="Proposals"
            value={proposalsCount}
            icon={FileText}
            trend={{ value: 5, isPositive: false }}
            className="bg-gradient-to-br from-orange-50 to-amber-50 dark:from-orange-950/30 dark:to-amber-950/30 border-orange-200 dark:border-orange-800"
          />
          <DashboardStatCard
            title="Birthdays This Month"
            value={birthdaysThisMonth.length}
            icon={Gift}
            className="bg-gradient-to-br from-pink-50 to-rose-50 dark:from-pink-950/30 dark:to-rose-950/30 border-pink-200 dark:border-pink-800"
          />
        </div>

        {/* Charts and Events Grid */}
        <div className="grid gap-6 lg:grid-cols-2">
          {/* Speaker Categories Chart */}
          <Card className="shadow-sm">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Speaker Categories
              </CardTitle>
            </CardHeader>
            <CardContent>
              <SpeakerCategoryChart data={categoryData} />
            </CardContent>
          </Card>

          {/* Recent Events */}
          <Card className="shadow-sm">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Upcoming Events
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentEvents.length > 0 ? (
                  recentEvents.map((event) => (
                    <div key={event.id} className="flex items-center justify-between p-3 rounded-lg bg-muted/50 hover:bg-muted transition-colors">
                      <div className="space-y-1">
                        <h4 className="font-medium text-sm">{event.title}</h4>
                        <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                          <Calendar className="h-3 w-3" />
                          <span>{format(new Date(event.date), 'MMM d, yyyy')}</span>
                          {event.location && (
                            <>
                              <span>•</span>
                              <span>{event.location}</span>
                            </>
                          )}
                        </div>
                      </div>
                      <Badge variant={isToday(new Date(event.date)) ? "default" : "secondary"}>
                        {isToday(new Date(event.date)) ? "Today" : "Upcoming"}
                      </Badge>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <Calendar className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p className="text-sm">No upcoming events</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Event Stats */}
        <Card className="shadow-sm">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Event Statistics
            </CardTitle>
          </CardHeader>
          <CardContent>
            <EventStats events={transformedEvents} />
          </CardContent>
        </Card>
      </div>
    </Layout>
  );
};

export default Index;
