import { useState, useEffect } from "react";
import { ChevronLeft, ChevronRight, Plus, Gift } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  format,
  startOfMonth,
  endOfMonth,
  eachDayOfInterval,
  isSameDay,
  isToday,
  addMonths,
  subMonths,
} from "date-fns";
import { cn } from "@/lib/utils";
import { useNavigate } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import Layout from "@/components/Layout";
import { Event } from "@/types/event";
import { Speaker } from "@/types/speaker";
import { getEvents } from "@/utils/eventStorage";

const Calendar = () => {
  const [events, setEvents] = useState<Event[]>([]);
  const [currentDate, setCurrentDate] = useState(new Date());
  const navigate = useNavigate();

  const { data: speakers = [] } = useQuery({
    queryKey: ["speakers"],
    queryFn: async () => {
      const { data, error } = await supabase.from("speakers").select("*");
      if (error) throw error;
      return (data || []) as Speaker[];
    },
  });

  useEffect(() => {
    setEvents(getEvents());
  }, []);

  const monthStart = startOfMonth(currentDate);
  const monthEnd = endOfMonth(currentDate);
  const daysInMonth = eachDayOfInterval({ start: monthStart, end: monthEnd });

  const getEventsForDay = (day: Date) => {
    return events.filter((event) => isSameDay(new Date(event.date), day));
  };

  const getBirthdaysForDay = (day: Date) => {
    return speakers.filter((speaker) => {
      if (!speaker.date_of_birth) return false;
      const birthday = new Date(speaker.date_of_birth);
      return (
        birthday.getMonth() === day.getMonth() &&
        birthday.getDate() === day.getDate()
      );
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Scheduled":
        return "bg-blue-100 text-blue-800";
      case "Completed":
        return "bg-green-100 text-green-800";
      case "Cancelled":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  // Get the start of the calendar grid (includes days from previous month)
  const calendarStart = new Date(monthStart);
  calendarStart.setDate(calendarStart.getDate() - monthStart.getDay());

  // Create array of 42 days (6 weeks)
  const calendarDays = [];
  for (let i = 0; i < 42; i++) {
    const day = new Date(calendarStart);
    day.setDate(day.getDate() + i);
    calendarDays.push(day);
  }

  return (
    <Layout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-foreground">Calendar</h1>
            <p className="text-muted-foreground">
              View your speaking events and speaker birthdays
            </p>
          </div>
          <Button onClick={() => navigate("/events")} className="gap-2">
            <Plus className="h-4 w-4" />
            Add New Event
          </Button>
        </div>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
            <CardTitle className="text-2xl font-bold">
              {format(currentDate, "MMMM yyyy")}
            </CardTitle>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentDate(subMonths(currentDate, 1))}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentDate(new Date())}
              >
                Today
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentDate(addMonths(currentDate, 1))}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {/* Calendar Header */}
            <div className="grid grid-cols-7 gap-1 mb-4">
              {["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"].map((day) => (
                <div
                  key={day}
                  className="p-2 text-center font-medium text-muted-foreground"
                >
                  {day}
                </div>
              ))}
            </div>

            {/* Calendar Grid */}
            <div className="grid grid-cols-7 gap-1">
              {calendarDays.map((day, index) => {
                const dayEvents = getEventsForDay(day);
                const dayBirthdays = getBirthdaysForDay(day);
                const isCurrentMonth =
                  day.getMonth() === currentDate.getMonth();
                const isDayToday = isToday(day);

                return (
                  <div
                    key={index}
                    className={cn(
                      "min-h-[120px] p-2 border rounded-md transition-colors",
                      isCurrentMonth ? "bg-background" : "bg-muted/30",
                      isDayToday && "bg-primary/10 border-primary",
                      "hover:bg-accent"
                    )}
                  >
                    <div
                      className={cn(
                        "text-sm font-medium mb-1",
                        isCurrentMonth
                          ? "text-foreground"
                          : "text-muted-foreground",
                        isDayToday && "text-primary font-bold"
                      )}
                    >
                      {format(day, "d")}
                    </div>

                    <div className="space-y-1">
                      {/* Events */}
                      {dayEvents.slice(0, 1).map((event) => (
                        <div
                          key={event.id}
                          className="text-xs p-1 rounded bg-primary/20 text-primary cursor-pointer hover:bg-primary/30 transition-colors"
                          onClick={() => navigate(`/event/${event.id}`)}
                        >
                          <div className="font-medium truncate">
                            {event.title}
                          </div>
                          <div className="text-xs opacity-75">
                            {event.startTime} - {event.endTime}
                          </div>
                        </div>
                      ))}

                      {/* Birthdays */}
                      {dayBirthdays.slice(0, 1).map((speaker) => (
                        <div
                          key={speaker.id}
                          className="text-xs p-1 rounded bg-pink-100 text-pink-800 cursor-pointer hover:bg-pink-200 transition-colors flex items-center gap-1"
                          onClick={() => navigate(`/speakers/${speaker.id}`)}
                        >
                          <Gift className="h-3 w-3" />
                          <div className="font-medium truncate">
                            {speaker.name}'s Birthday
                          </div>
                        </div>
                      ))}

                      {/* Show more indicator */}
                      {dayEvents.length + dayBirthdays.length > 2 && (
                        <div className="text-xs text-muted-foreground cursor-pointer hover:text-foreground">
                          +{dayEvents.length + dayBirthdays.length - 2} more
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>

        {/* Upcoming Events and Birthdays */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Upcoming Events</CardTitle>
            </CardHeader>
            <CardContent>
              {events
                .filter(
                  (event) =>
                    new Date(event.date) >= new Date() &&
                    event.status === "Scheduled"
                )
                .sort(
                  (a, b) =>
                    new Date(a.date).getTime() - new Date(b.date).getTime()
                )
                .slice(0, 5)
                .map((event) => (
                  <div
                    key={event.id}
                    className="flex items-center justify-between py-3 border-b last:border-b-0 cursor-pointer hover:bg-muted"
                    onClick={() => navigate(`/event/${event.id}`)}
                  >
                    <div className="space-y-1">
                      <div className="font-medium">{event.title}</div>
                      <div className="text-sm text-muted-foreground">
                        {format(new Date(event.date), "PPP")} •{" "}
                        {event.startTime} - {event.endTime}
                      </div>
                    </div>
                    <Badge className={getStatusColor(event.status)}>
                      {event.status}
                    </Badge>
                  </div>
                ))}
              {events.filter(
                (event) =>
                  new Date(event.date) >= new Date() &&
                  event.status === "Scheduled"
              ).length === 0 && (
                <div className="text-center py-8 text-muted-foreground">
                  No upcoming events scheduled
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Gift className="h-5 w-5" />
                Upcoming Birthdays
              </CardTitle>
            </CardHeader>
            <CardContent>
              {speakers
                .filter((speaker) => {
                  if (!speaker.date_of_birth) return false;
                  const birthday = new Date(speaker.date_of_birth);
                  const today = new Date();
                  const thisYear = today.getFullYear();
                  const nextBirthday = new Date(
                    thisYear,
                    birthday.getMonth(),
                    birthday.getDate()
                  );

                  if (nextBirthday < today) {
                    nextBirthday.setFullYear(thisYear + 1);
                  }

                  const daysUntil = Math.ceil(
                    (nextBirthday.getTime() - today.getTime()) /
                      (1000 * 60 * 60 * 24)
                  );
                  return daysUntil <= 30;
                })
                .sort((a, b) => {
                  const today = new Date();
                  const thisYear = today.getFullYear();
                  const aBirthday = new Date(
                    thisYear,
                    new Date(a.date_of_birth!).getMonth(),
                    new Date(a.date_of_birth!).getDate()
                  );
                  const bBirthday = new Date(
                    thisYear,
                    new Date(b.date_of_birth!).getMonth(),
                    new Date(b.date_of_birth!).getDate()
                  );

                  if (aBirthday < today) aBirthday.setFullYear(thisYear + 1);
                  if (bBirthday < today) bBirthday.setFullYear(thisYear + 1);

                  return aBirthday.getTime() - bBirthday.getTime();
                })
                .slice(0, 5)
                .map((speaker) => {
                  const birthday = new Date(speaker.date_of_birth!);
                  const today = new Date();
                  const thisYear = today.getFullYear();
                  const nextBirthday = new Date(
                    thisYear,
                    birthday.getMonth(),
                    birthday.getDate()
                  );

                  if (nextBirthday < today) {
                    nextBirthday.setFullYear(thisYear + 1);
                  }

                  const daysUntil = Math.ceil(
                    (nextBirthday.getTime() - today.getTime()) /
                      (1000 * 60 * 60 * 24)
                  );

                  return (
                    <div
                      key={speaker.id}
                      className="flex items-center justify-between py-3 border-b last:border-b-0 cursor-pointer hover:bg-muted"
                      onClick={() => navigate(`/speakers/${speaker.id}`)}
                    >
                      <div className="space-y-1">
                        <div className="font-medium">{speaker.name}</div>
                        <div className="text-sm text-muted-foreground">
                          {format(nextBirthday, "MMM d")} •{" "}
                          {daysUntil === 0 ? "Today!" : `${daysUntil} days`}
                        </div>
                      </div>
                      <Gift className="h-4 w-4 text-pink-500" />
                    </div>
                  );
                })}
              {speakers.filter((s) => s.date_of_birth).length === 0 && (
                <div className="text-center py-8 text-muted-foreground">
                  No upcoming birthdays
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </Layout>
  );
};

export default Calendar;
