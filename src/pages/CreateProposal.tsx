import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import Layout from "@/components/Layout";
import { ProposalHeader } from "@/components/proposals/ProposalHeader";
import { ClientInformation } from "@/components/proposals/ClientInformation";
import { EventDetails } from "@/components/proposals/EventDetails";
import { SimpleSpeakerSelection } from "@/components/proposals/SimpleSpeakerSelection";
import { ProposalActions } from "@/components/proposals/ProposalActions";
import { TemplateManager } from "@/components/TemplateManager";
import { seedSpeakersIfEmpty } from "@/utils/seedSpeakers";

export default function CreateProposal() {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [showTemplateManager, setShowTemplateManager] = useState(false);

  const [clientInfo, setClientInfo] = useState({
    name: "",
    email: "",
    company: "",
    phone: "",
    notes: "",
  });

  const [eventInfo, setEventInfo] = useState({
    name: "",
    date: "",
    time: "",
    duration: "",
    location: "",
    description: "",
    audience_size: "",
    event_type: "",
    budget_range: "",
    special_requirements: "",
  });

  const [selectedSpeakers, setSelectedSpeakers] = useState<string[]>([]);

  // Fetch speakers for selection
  const fetchSpeakers = async () => {
    const { data, error } = await supabase
      .from("speakers")
      .select("*")
      .order("name");

    if (error) throw error;
    return data || [];
  };

  const { data: speakers = [] } = useQuery({
    queryKey: ["speakers"],
    queryFn: fetchSpeakers,
  });

  // Seed speakers if database is empty
  useEffect(() => {
    const initializeSpeakers = async () => {
      await seedSpeakersIfEmpty();
    };
    initializeSpeakers();
  }, []);

  const handleBack = () => {
    navigate("/proposals");
  };

  const handleEditTemplate = () => {
    setShowTemplateManager(true);
  };

  const handleSave = async () => {
    try {
      // Validate required fields
      if (
        !clientInfo.name ||
        !clientInfo.email ||
        !eventInfo.name ||
        !eventInfo.date
      ) {
        toast({
          title: "Validation Error",
          description: "Please fill in all required fields.",
          variant: "destructive",
        });
        return;
      }

      // Create proposal
      const proposalData = {
        event_name: eventInfo.name,
        status: "draft",
        details: {
          client_name: clientInfo.name,
          client_email: clientInfo.email,
          client_company: clientInfo.company,
          client_phone: clientInfo.phone,
          client_notes: clientInfo.notes,
          event_date: eventInfo.date,
          event_time: eventInfo.time,
          event_duration: eventInfo.duration,
          event_location: eventInfo.location,
          event_description: eventInfo.description,
          audience_size: eventInfo.audience_size,
          event_type: eventInfo.event_type,
          budget_range: eventInfo.budget_range,
          special_requirements: eventInfo.special_requirements,
          speakers: selectedSpeakers,
          total_budget: eventInfo.budget_range,
        },
        speaker_id: selectedSpeakers[0] || null, // Use first selected speaker as primary
      };

      const { error } = await supabase.from("proposals").insert([proposalData]);

      if (error) throw error;

      toast({
        title: "Success",
        description: "Proposal created successfully!",
      });
      navigate("/proposals");
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  return (
    <Layout>
      <div className="container mx-auto px-6 py-8 space-y-8">
        <ProposalHeader
          isEditing={false}
          onBack={handleBack}
          onEditTemplate={handleEditTemplate}
        />

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div className="space-y-6">
            <ClientInformation
              clientInfo={clientInfo}
              setClientInfo={setClientInfo}
            />

            <EventDetails
              eventDetails={eventInfo}
              setEventDetails={setEventInfo}
            />
          </div>

          <div className="space-y-6">
            <SimpleSpeakerSelection
              speakers={speakers}
              selectedSpeakers={selectedSpeakers}
              setSelectedSpeakers={setSelectedSpeakers}
            />

            <ProposalActions
              onSave={handleSave}
              onCancel={handleBack}
              isEditing={false}
            />
          </div>
        </div>

        <TemplateManager
          open={showTemplateManager}
          onOpenChange={setShowTemplateManager}
          mode="manage"
        />
      </div>
    </Layout>
  );
}
