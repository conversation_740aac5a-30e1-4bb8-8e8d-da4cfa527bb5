
import { Speaker } from "@/types/speaker";

export const generateSpeakerCsvTemplate = () => {
  const headers = [
    'name',
    'bio',
    'category',
    'location',
    'experience',
    'rate',
    'specialties',
    'availability',
    'date_of_birth'
  ];

  const sampleData = [
    '<PERSON>',
    'Expert in business strategy and leadership development',
    'Business',
    'New York, NY',
    '15+ years in corporate leadership',
    '25000',
    'Leadership,Strategy,Innovation',
    'Available',
    '1975-06-15'
  ];

  const csvContent = [
    headers.join(','),
    sampleData.join(',')
  ].join('\n');

  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);
  link.setAttribute('href', url);
  link.setAttribute('download', 'speakers_template.csv');
  link.style.visibility = 'hidden';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

export const parseCsvToSpeakers = (csvText: string): Partial<Speaker>[] => {
  const lines = csvText.split('\n').filter(line => line.trim());
  if (lines.length < 2) return [];

  const headers = lines[0].split(',').map(h => h.trim().toLowerCase());
  const speakers: Partial<Speaker>[] = [];
  
  for (let i = 1; i < lines.length; i++) {
    const values = lines[i].split(',').map(v => v.trim());
    if (values.length < headers.length) continue;

    const speaker: Partial<Speaker> = {};
    
    headers.forEach((header, index) => {
      const value = values[index];
      if (!value) return;

      switch (header) {
        case 'name':
          speaker.name = value;
          break;
        case 'bio':
          speaker.bio = value;
          break;
        case 'category':
          speaker.category = value;
          break;
        case 'location':
          speaker.location = value;
          break;
        case 'experience':
          speaker.experience = value;
          break;
        case 'rate':
          const rate = parseInt(value);
          if (!isNaN(rate)) speaker.rate = rate;
          break;
        case 'specialties':
          if (value) {
            speaker.specialties = value.split(';').map(s => s.trim()).filter(s => s);
          }
          break;
        case 'availability':
          if (['Available', 'Busy', 'Unavailable'].includes(value)) {
            speaker.availability = value;
          }
          break;
        case 'date_of_birth':
          if (value && /^\d{4}-\d{2}-\d{2}$/.test(value)) {
            speaker.date_of_birth = value;
          }
          break;
      }
    });

    if (speaker.name) {
      speakers.push(speaker);
    }
  }

  return speakers;
};

export const exportSpeakersToCSV = (speakers: Speaker[]) => {
  const headers = [
    'Name',
    'Bio',
    'Category',
    'Location',
    'Experience',
    'Rate',
    'Specialties',
    'Availability',
    'Date of Birth'
  ];

  const csvData = speakers.map(speaker => [
    speaker.name || '',
    speaker.bio || '',
    speaker.category || '',
    speaker.location || '',
    speaker.experience || '',
    speaker.rate?.toString() || '',
    speaker.specialties?.join(';') || '',
    speaker.availability || '',
    speaker.date_of_birth || ''
  ]);

  const csvContent = [
    headers.join(','),
    ...csvData.map(row => row.map(cell => `"${cell}"`).join(','))
  ].join('\n');

  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);
  link.setAttribute('href', url);
  link.setAttribute('download', `speakers_export_${new Date().toISOString().split('T')[0]}.csv`);
  link.style.visibility = 'hidden';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};
