import { Event } from "@/types/event";

const EVENTS_KEY = 'speaker-agency-events';

export const saveEvent = (event: Event): void => {
  const events = getEvents();
  const existingIndex = events.findIndex(e => e.id === event.id);

  if (existingIndex >= 0) {
    events[existingIndex] = event;
  } else {
    events.push(event);
  }

  localStorage.setItem(EVENTS_KEY, JSON.stringify(events));
};

export const getEvents = (): Event[] => {
  const stored = localStorage.getItem(EVENTS_KEY);
  if (!stored) return [];

  // Dates are stored as strings, which matches the Event type.
  // Components should handle parsing to Date objects when needed.
  return JSON.parse(stored);
};

export const deleteEvent = (id: string): void => {
  const events = getEvents().filter(event => event.id !== id);
  localStorage.setItem(EVENTS_KEY, JSON.stringify(events));
};

export const getEventById = (id: string): Event | undefined => {
  return getEvents().find(event => event.id === id);
};
