
import PptxGenJS from 'pptxgenjs';
import { Proposal } from '../types/proposal';

const toDataURL = (url: string): Promise<string> =>
  fetch(url)
    .then(response => {
      if (!response.ok) {
        throw new Error(`Failed to fetch image: ${response.status} ${response.statusText}`);
      }
      return response.blob();
    })
    .then(blob => new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onloadend = () => resolve(reader.result as string);
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    }));

const theme = {
  colors: {
    primary: '1e3a8a',
    secondary: '3b82f6',
    accent: 'f59e0b',
    textPrimary: '1f2937',
    textSecondary: '6b7280',
    background: 'FFFFFF',
    lightGray: 'f8fafc',
    darkBlue: '1e293b',
    overlay: '000000', // For background overlay
  },
  fonts: {
    primary: 'Inter',
    heading: 'Inter',
  }
};

export const generateProposalPPTX = async (proposal: Proposal) => {
  try {
    const pptx = new PptxGenJS();
    // Use standard 16:9 layout with default dimensions
    pptx.layout = 'LAYOUT_16x9';

    // Get image URLs
    const backgroundImageUrl = `${window.location.origin}/images/background.jpeg`;
    const logoImageUrl = `${window.location.origin}/images/logo.png`;
    const sideImageUrl = `${window.location.origin}/images/image.png`;

    // Convert images to base64
    let backgroundImageBase64: string | null = null;
    let logoImageBase64: string | null = null;
    let sideImageBase64: string | null = null;

    try {
      backgroundImageBase64 = await toDataURL(backgroundImageUrl);
      logoImageBase64 = await toDataURL(logoImageUrl);
      sideImageBase64 = await toDataURL(sideImageUrl);
    } catch (error) {
      console.warn('Could not load images:', error);
    }

    // Define slide master with side image
    pptx.defineSlideMaster({
      title: 'MASTER_SLIDE',
      background: { color: theme.colors.background },
      objects: sideImageBase64 ? [
        {
          'image': {
            data: sideImageBase64,
            x: 0, y: 0, w: '15%', h: '100%'
          }
        }
      ] : []
    });

    // 1. Cover Page
    const coverSlide = pptx.addSlide();

    // Add background image
    if (backgroundImageBase64) {
      coverSlide.addImage({
        data: backgroundImageBase64,
        x: 0, y: 0, w: '100%', h: '100%'
      });
    } else {
      coverSlide.addShape(pptx.ShapeType.rect, {
        x: 0, y: 0, w: '100%', h: '100%',
        fill: { color: theme.colors.primary }
      });
    }

    // Add overlay
    coverSlide.addShape(pptx.ShapeType.rect, {
      x: 0, y: 0, w: '100%', h: '100%',
      fill: { color: theme.colors.overlay, transparency: 40 }
    });

    // Add logo (perfectly centered) - same dimensions as thank you page
    if (logoImageBase64) {
      coverSlide.addImage({
        data: logoImageBase64,
        x: '41.5%', y: '30%', w: '17%', h: '16%'
      });
    }

    // Add main text (perfectly centered) - minimal margin from logo
    coverSlide.addText('The #1 Speakers Bureau in the MENA Region since 2016', {
      x: '10%', y: '48%', w: '80%', h: '8%',
      align: 'center',
      fontSize: 16,
      color: 'FFFFFF',
      fontFace: theme.fonts.primary,
      bold: false
    });

    // 2. About Us
    const aboutSlide = pptx.addSlide({ masterName: 'MASTER_SLIDE' });
    aboutSlide.addText('About Us', {
      x: '20%', y: '8%', w: '75%', h: '12%',
      fontSize: 32, bold: false, color: theme.colors.primary,
      fontFace: theme.fonts.primary
    });

    aboutSlide.addShape(pptx.ShapeType.line, {
      x: '20%', y: '20%', w: '25%', h: 0,
      line: { color: theme.colors.accent, width: 3 }
    });

    const aboutText = `We are the leading speakers bureau in the Middle East and North Africa region, connecting organizations with world-class speakers since 2016.

Our mission is to inspire, educate, and transform audiences through exceptional speaking experiences.

Key Services:
• Keynote Speakers
• Workshop Facilitators
• Panel Moderators
• Industry Experts
• Motivational Speakers

With over 500+ successful events and a network of 200+ premium speakers, we deliver excellence in every engagement.`;

    aboutSlide.addText(aboutText, {
      x: '20%', y: '25%', w: '75%', h: '65%',
      fontSize: 14, color: theme.colors.textPrimary,
      fontFace: theme.fonts.primary
    });

    // 3. Event Overview
    const eventSlide = pptx.addSlide({ masterName: 'MASTER_SLIDE' });
    eventSlide.addText('Event Overview', {
      x: '20%', y: '8%', w: '75%', h: '12%',
      fontSize: 32, bold: false, color: theme.colors.primary,
      fontFace: theme.fonts.primary
    });

    eventSlide.addShape(pptx.ShapeType.line, {
      x: '20%', y: '20%', w: '25%', h: 0,
      line: { color: theme.colors.accent, width: 3 }
    });

    const eventName = proposal.event?.eventName || 'Event Name';
    eventSlide.addText(eventName, {
      x: '20%', y: '25%', w: '75%', h: '10%',
      fontSize: 24, bold: false, color: theme.colors.textPrimary,
      fontFace: theme.fonts.primary
    });

    const eventDetails = `Date: ${proposal.event?.eventDate || 'TBD'}
Location: ${proposal.event?.eventLocation || 'TBD'}
Event Type: ${proposal.event?.eventType || 'Conference'}
Audience Size: ${proposal.event?.audience || 'TBD'}
Budget Range: ${proposal.event?.budget || 'TBD'}

${proposal.event?.description || 'Event description will be provided.'}`;

    eventSlide.addText(eventDetails, {
      x: '20%', y: '40%', w: '75%', h: '50%',
      fontSize: 14, color: theme.colors.textPrimary,
      fontFace: theme.fonts.primary
    });

    // 4. Speakers Divider
    const speakersSlide = pptx.addSlide();

    // Add background image
    if (backgroundImageBase64) {
      speakersSlide.addImage({
        data: backgroundImageBase64,
        x: 0, y: 0, w: '100%', h: '100%'
      });
    }

    // Add overlay
    speakersSlide.addShape(pptx.ShapeType.rect, {
      x: 0, y: 0, w: '100%', h: '100%',
      fill: { color: theme.colors.overlay, transparency: 50 }
    });

    speakersSlide.addText('Speakers', {
      x: '10%', y: '40%', w: '80%', h: '20%',
      align: 'center',
      fontSize: 48,
      color: 'FFFFFF',
      fontFace: theme.fonts.heading,
      bold: true
    });

    // 5. Speaker Profile Pages
    for (const speakerData of proposal.speakers) {
      const speaker = speakerData.speaker;
      const speakerSlide = pptx.addSlide({ masterName: 'MASTER_SLIDE' });

      speakerSlide.addText(speaker.name, {
        x: '20%', y: '8%', w: '60%', h: '12%',
        fontSize: 28, bold: false, color: theme.colors.primary,
        fontFace: theme.fonts.primary
      });

      // Add speaker image
      if (speaker.image) {
        try {
          const speakerImageBase64 = await toDataURL(speaker.image);
          speakerSlide.addImage({
            data: speakerImageBase64,
            x: '70%', y: '20%', w: '25%', h: '45%'
          });
        } catch (error) {
          console.warn('Could not load speaker image:', error);
        }
      }

      speakerSlide.addText(speaker.bio || 'Professional speaker with extensive experience.', {
        x: '20%', y: '25%', w: '45%', h: '35%',
        fontSize: 12, color: theme.colors.textPrimary,
        fontFace: theme.fonts.primary
      });

      const speakerInfo = `Category: ${speaker.category || 'Professional Speaker'}
Location: ${speaker.location || 'International'}
Experience: ${speaker.experience || 'Extensive experience'}
Fee: $${(speaker.rate || 0).toLocaleString()} USD`;

      speakerSlide.addText(speakerInfo, {
        x: '20%', y: '70%', w: '75%', h: '20%',
        fontSize: 11, color: theme.colors.textSecondary,
        fontFace: theme.fonts.primary
      });
    }

    // 6. Case Study Divider
    const caseStudySlide = pptx.addSlide();

    // Add background image
    if (backgroundImageBase64) {
      caseStudySlide.addImage({
        data: backgroundImageBase64,
        x: 0, y: 0, w: '100%', h: '100%'
      });
    }

    // Add overlay
    caseStudySlide.addShape(pptx.ShapeType.rect, {
      x: 0, y: 0, w: '100%', h: '100%',
      fill: { color: theme.colors.overlay, transparency: 50 }
    });

    caseStudySlide.addText('Case Study', {
      x: '10%', y: '40%', w: '80%', h: '20%',
      align: 'center',
      fontSize: 48,
      color: 'FFFFFF',
      fontFace: theme.fonts.heading,
      bold: true
    });

    // 7. Case Study Content
    const caseStudyContentSlide = pptx.addSlide({ masterName: 'MASTER_SLIDE' });

    caseStudyContentSlide.addText('NSTI Family Festival In Dubai', {
      x: '20%', y: '8%', w: '75%', h: '12%',
      fontSize: 28, bold: false, color: theme.colors.primary,
      fontFace: theme.fonts.primary
    });

    const caseStudyText = `The National Science, Technology, and Innovation Festival (NSTI) champions the spirit of discovery and innovation, setting a stage for nurturing passions in science, technology, innovation, and entrepreneurship.

Results:
• Spotlighted over 100 innovative projects from high schools across the UAE
• Featured 28 speakers who contributed to comprehensive discourse
• Engaged early learners through the Young Innovators Competition
• Brought together diverse speakers and government officials
• Enhanced community interaction and educational engagement`;

    caseStudyContentSlide.addText(caseStudyText, {
      x: '20%', y: '25%', w: '75%', h: '65%',
      fontSize: 14, color: theme.colors.textPrimary,
      fontFace: theme.fonts.primary
    });

    // 8. Do You Need Help?
    const helpSlide = pptx.addSlide({ masterName: 'MASTER_SLIDE' });
    helpSlide.addText('Do You Need Help?', {
      x: '20%', y: '8%', w: '75%', h: '12%',
      fontSize: 32, bold: false, color: theme.colors.primary,
      fontFace: theme.fonts.primary
    });

    const helpText = `We're here to make your event a success. Our team of experts will work with you to:

• Find the perfect speakers for your audience
• Manage all logistics and coordination
• Ensure seamless event execution
• Provide ongoing support throughout the process

Contact us today to discuss your event needs and let us help you create an unforgettable experience.`;

    helpSlide.addText(helpText, {
      x: '20%', y: '25%', w: '75%', h: '65%',
      fontSize: 14, color: theme.colors.textPrimary,
      fontFace: theme.fonts.primary
    });

    // 9. Our Clients
    const clientsSlide = pptx.addSlide({ masterName: 'MASTER_SLIDE' });
    clientsSlide.addText('Our Clients', {
      x: '20%', y: '8%', w: '75%', h: '12%',
      fontSize: 32, bold: false, color: theme.colors.primary,
      fontFace: theme.fonts.primary
    });

    const clientsText = `We are proud to have worked with leading organizations across the MENA region:

• Government Entities
• Fortune 500 Companies
• Educational Institutions
• Non-Profit Organizations
• Technology Companies
• Healthcare Organizations
• Financial Services
• Media & Entertainment

Our clients trust us to deliver exceptional speaking experiences that inspire, educate, and transform their audiences.`;

    clientsSlide.addText(clientsText, {
      x: '20%', y: '25%', w: '75%', h: '65%',
      fontSize: 14, color: theme.colors.textPrimary,
      fontFace: theme.fonts.primary
    });

    // 10. Thank You Page
    const thankYouSlide = pptx.addSlide();

    // Add background image
    if (backgroundImageBase64) {
      thankYouSlide.addImage({
        data: backgroundImageBase64,
        x: 0, y: 0, w: '100%', h: '100%'
      });
    }

    // Add overlay
    thankYouSlide.addShape(pptx.ShapeType.rect, {
      x: 0, y: 0, w: '100%', h: '100%',
      fill: { color: theme.colors.overlay, transparency: 40 }
    });

    // Left column - Logo (173pt width)
    if (logoImageBase64) {
      thankYouSlide.addImage({
        data: logoImageBase64,
        x: '12%', y: '40%', w: '17%', h: '16%'
      });
    }

    // Right column - Contact Info
    const contactInfo = `Contact Us

Email: <EMAIL>
Phone: +971 4 123 4567
Website: www.mena-speakers.com

Address:
MENA Speakers Bureau
Dubai, UAE

Thank you for considering us for your event!`;

    thankYouSlide.addText(contactInfo, {
      x: '55%', y: '25%', w: '35%', h: '50%',
      fontSize: 16, color: 'FFFFFF',
      fontFace: theme.fonts.primary,
      align: 'left'
    });

    // Generate and download
    const eventNameForFile = proposal.event?.eventName || 'untitled-event';
    const fileName = `mena-speakers-proposal-${eventNameForFile.replace(/\s+/g, '-').toLowerCase()}-${Date.now()}.pptx`;
    await pptx.writeFile({ fileName });
  } catch (error) {
    console.error("Error during PPTX generation:", error);
    throw error;
  }
};
