
import { supabase } from "@/integrations/supabase/client";
import { Speaker } from "@/types/speaker";

export const getTodaysBirthdays = async (): Promise<Speaker[]> => {
  const today = new Date();
  const monthDay = `${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;
  
  const { data, error } = await supabase
    .from('speakers')
    .select('*')
    .not('date_of_birth', 'is', null)
    .like('date_of_birth', `%-${monthDay}`);

  if (error) {
    console.error('Error fetching birthdays:', error);
    return [];
  }

  return (data || []) as Speaker[];
};

export const sendBirthdayEmail = async (speaker: Speaker) => {
  try {
    const { error } = await supabase.functions.invoke('send-birthday-email', {
      body: {
        speakerName: speaker.name,
      }
    });

    if (error) {
      console.error('Error sending birthday email:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error invoking birthday email function:', error);
    return false;
  }
};
