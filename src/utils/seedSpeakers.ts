import { supabase } from '@/integrations/supabase/client';

export const sampleSpeakers = [
  {
    name: "Dr. <PERSON>",
    bio: "AI researcher and tech entrepreneur with 15+ years in machine learning and data science. Former Google AI researcher, now CEO of TechVision AI.",
    category: "Technology",
    rate: 25000,
    location: "San Francisco, CA",
    experience: "15+ years in AI and Machine Learning",
    specialties: ["Artificial Intelligence", "Machine Learning", "Data Science", "Tech Innovation"],
    availability: "Available"
  },
  {
    name: "<PERSON>",
    bio: "Former Google VP of Engineering, now startup advisor and cybersecurity expert. Author of 'Secure by Design' and frequent keynote speaker.",
    category: "Technology",
    rate: 30000,
    location: "Austin, TX",
    experience: "20+ years in Technology Leadership",
    specialties: ["Cybersecurity", "Cloud Computing", "Digital Transformation", "Tech Leadership"],
    availability: "Available"
  },
  {
    name: "<PERSON>",
    bio: "Blockchain pioneer and cryptocurrency expert, founder of CryptoSecure. Leading voice in decentralized finance and digital assets.",
    category: "Technology",
    rate: 28000,
    location: "Miami, FL",
    experience: "12+ years in Blockchain and Fintech",
    specialties: ["Blockchain", "Cryptocurrency", "Fintech", "Digital Innovation"],
    availability: "Available"
  },
  {
    name: "<PERSON>",
    bio: "Fortune 500 CEO and leadership consultant, author of 'Leading in the Digital Age'. Former CEO of Wellington Industries.",
    category: "Business",
    rate: 35000,
    location: "Chicago, IL",
    experience: "25+ years in Executive Leadership",
    specialties: ["Leadership", "Strategic Planning", "Digital Transformation", "Change Management"],
    availability: "Available"
  },
  {
    name: "Amanda Foster",
    bio: "Sales strategy expert and business development guru. Built and scaled sales teams for multiple Fortune 500 companies.",
    category: "Business",
    rate: 22000,
    location: "New York, NY",
    experience: "18+ years in Sales Leadership",
    specialties: ["Sales Strategy", "Team Building", "Performance Management", "Customer Relations"],
    availability: "Available"
  },
  {
    name: "David Kim",
    bio: "Serial entrepreneur and startup mentor. Founded 3 successful startups, now angel investor and business advisor.",
    category: "Business",
    rate: 27000,
    location: "Seattle, WA",
    experience: "20+ years in Entrepreneurship",
    specialties: ["Entrepreneurship", "Startup Strategy", "Investment", "Business Development"],
    availability: "Available"
  },
  {
    name: "Dr. Lisa Thompson",
    bio: "Wellness expert and mindfulness coach. PhD in Psychology, specializing in workplace wellness and stress management.",
    category: "Health & Wellness",
    rate: 18000,
    location: "Los Angeles, CA",
    experience: "12+ years in Wellness Coaching",
    specialties: ["Mindfulness", "Stress Management", "Work-Life Balance", "Mental Health"],
    availability: "Available"
  },
  {
    name: "Michael Brooks",
    bio: "Performance psychologist and team dynamics expert. Works with Olympic athletes and Fortune 500 executives.",
    category: "Health & Wellness",
    rate: 20000,
    location: "Denver, CO",
    experience: "15+ years in Performance Psychology",
    specialties: ["Performance Psychology", "Team Dynamics", "Goal Setting", "Resilience"],
    availability: "Available"
  },
  {
    name: "Prof. Rachel Green",
    bio: "Educational technology pioneer and learning innovation expert. Professor at Stanford, consultant for major EdTech companies.",
    category: "Education",
    rate: 16000,
    location: "Palo Alto, CA",
    experience: "20+ years in Educational Technology",
    specialties: ["Educational Technology", "Learning Innovation", "Curriculum Development", "Student Engagement"],
    availability: "Available"
  },
  {
    name: "Carlos Martinez",
    bio: "Corporate trainer and skills development specialist. Designed training programs for Google, Microsoft, and Amazon.",
    category: "Education",
    rate: 15000,
    location: "Phoenix, AZ",
    experience: "14+ years in Corporate Training",
    specialties: ["Skills Training", "Professional Development", "Leadership Training", "Team Building"],
    availability: "Available"
  },
  {
    name: "Tony Richardson",
    bio: "Former Navy SEAL turned motivational speaker, expert in building mental toughness and resilience. Author of 'Unbreakable Mind'.",
    category: "Motivational",
    rate: 17000,
    location: "Nashville, TN",
    experience: "8+ years in Motivational Speaking",
    specialties: ["Mental Toughness", "Resilience", "Team Building", "Leadership Under Pressure"],
    availability: "Available"
  },
  {
    name: "Jennifer Walsh",
    bio: "Inspirational speaker and life coach. Overcame personal challenges to become a successful entrepreneur and bestselling author.",
    category: "Motivational",
    rate: 14000,
    location: "Atlanta, GA",
    experience: "10+ years in Motivational Speaking",
    specialties: ["Personal Growth", "Overcoming Adversity", "Goal Achievement", "Inspiration"],
    availability: "Available"
  }
];

export const seedSpeakersIfEmpty = async () => {
  try {
    // Check if speakers already exist
    const { data: existingSpeakers, error: checkError } = await supabase
      .from('speakers')
      .select('id')
      .limit(1);

    if (checkError) {
      console.error('Error checking speakers:', checkError);
      return false;
    }

    // If speakers already exist, don't seed
    if (existingSpeakers && existingSpeakers.length > 0) {
      console.log('Speakers already exist, skipping seed');
      return true;
    }

    // Insert sample speakers
    const { error: insertError } = await supabase
      .from('speakers')
      .insert(sampleSpeakers);

    if (insertError) {
      console.error('Error seeding speakers:', insertError);
      return false;
    }

    console.log('Successfully seeded speakers');
    return true;
  } catch (error) {
    console.error('Error in seedSpeakersIfEmpty:', error);
    return false;
  }
};
