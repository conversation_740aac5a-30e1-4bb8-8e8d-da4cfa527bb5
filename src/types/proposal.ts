
export interface ProposalEvent {
  eventName: string;
  eventDate: string;
  eventLocation: string;
  eventType: string;
  audience: string;
  budget: string;
  description: string;
}

export interface ProposalSpeaker {
  speaker: {
    id: string;
    name: string;
    bio: string;
    category: string;
    image: string;
    rate: number;
    location: string;
    experience: string;
    specialties: string[];
    availability: 'Available' | 'Busy' | 'Unavailable';
  };
  role: string;
  notes?: string;
}

export type ProposalStatus = "Draft" | "Pending" | "Approved" | "Rejected";

export interface Proposal {
  id: string;
  clientName: string;
  clientEmail: string;
  event: ProposalEvent;
  speakers: ProposalSpeaker[];
  totalBudget: number;
  createdAt: string;
  pdfPath?: string;
  status?: ProposalStatus;
}

export interface ProposalTemplateSettings {
  id: string;
  user_id: string;
  cover_page_title: string;
  cover_page_image_url: string;
  about_us_mission: string;
  created_at: string;
  updated_at: string;
}
