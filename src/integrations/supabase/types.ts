export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  // Allows to automatically instanciate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: "12.2.3 (519615d)"
  }
  public: {
    Tables: {
      bitrix_deals: {
        Row: {
          bitrix_deal_id: string
          client_email: string | null
          client_name: string | null
          created_at: string
          deal_amount: number | null
          deal_stage: string | null
          deal_title: string
          event_date: string | null
          event_location: string | null
          id: string
          speaker_requirements: string | null
          sync_status: string | null
          synced_to_event_id: string | null
          updated_at: string
        }
        Insert: {
          bitrix_deal_id: string
          client_email?: string | null
          client_name?: string | null
          created_at?: string
          deal_amount?: number | null
          deal_stage?: string | null
          deal_title: string
          event_date?: string | null
          event_location?: string | null
          id?: string
          speaker_requirements?: string | null
          sync_status?: string | null
          synced_to_event_id?: string | null
          updated_at?: string
        }
        Update: {
          bitrix_deal_id?: string
          client_email?: string | null
          client_name?: string | null
          created_at?: string
          deal_amount?: number | null
          deal_stage?: string | null
          deal_title?: string
          event_date?: string | null
          event_location?: string | null
          id?: string
          speaker_requirements?: string | null
          sync_status?: string | null
          synced_to_event_id?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "bitrix_deals_synced_to_event_id_fkey"
            columns: ["synced_to_event_id"]
            isOneToOne: false
            referencedRelation: "events"
            referencedColumns: ["id"]
          },
        ]
      }
      categories: {
        Row: {
          created_at: string
          id: string
          name: string
        }
        Insert: {
          created_at?: string
          id?: string
          name: string
        }
        Update: {
          created_at?: string
          id?: string
          name?: string
        }
        Relationships: []
      }
      email_settings: {
        Row: {
          created_at: string
          id: string
          stamp_mailbox: string | null
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          stamp_mailbox?: string | null
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          stamp_mailbox?: string | null
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      employees: {
        Row: {
          created_at: string
          email: string
          id: string
          image_url: string | null
          name: string
          role: string | null
        }
        Insert: {
          created_at?: string
          email: string
          id?: string
          image_url?: string | null
          name: string
          role?: string | null
        }
        Update: {
          created_at?: string
          email?: string
          id?: string
          image_url?: string | null
          name?: string
          role?: string | null
        }
        Relationships: []
      }
      events: {
        Row: {
          created_at: string
          date: string
          description: string | null
          endTime: string | null
          id: string
          location: string | null
          responsible_person_id: string | null
          speakerIds: string[] | null
          startTime: string | null
          status: string | null
          title: string
        }
        Insert: {
          created_at?: string
          date: string
          description?: string | null
          endTime?: string | null
          id?: string
          location?: string | null
          responsible_person_id?: string | null
          speakerIds?: string[] | null
          startTime?: string | null
          status?: string | null
          title: string
        }
        Update: {
          created_at?: string
          date?: string
          description?: string | null
          endTime?: string | null
          id?: string
          location?: string | null
          responsible_person_id?: string | null
          speakerIds?: string[] | null
          startTime?: string | null
          status?: string | null
          title?: string
        }
        Relationships: [
          {
            foreignKeyName: "events_responsible_person_id_fkey"
            columns: ["responsible_person_id"]
            isOneToOne: false
            referencedRelation: "employees"
            referencedColumns: ["id"]
          },
        ]
      }
      profiles: {
        Row: {
          avatar_url: string | null
          full_name: string | null
          id: string
          updated_at: string | null
        }
        Insert: {
          avatar_url?: string | null
          full_name?: string | null
          id: string
          updated_at?: string | null
        }
        Update: {
          avatar_url?: string | null
          full_name?: string | null
          id?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      proposal_template_settings: {
        Row: {
          about_us_mission: string | null
          cover_page_image_url: string | null
          cover_page_title: string | null
          created_at: string
          id: string
          updated_at: string
          user_id: string
        }
        Insert: {
          about_us_mission?: string | null
          cover_page_image_url?: string | null
          cover_page_title?: string | null
          created_at?: string
          id?: string
          updated_at?: string
          user_id?: string
        }
        Update: {
          about_us_mission?: string | null
          cover_page_image_url?: string | null
          cover_page_title?: string | null
          created_at?: string
          id?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      proposal_templates: {
        Row: {
          about_us_mission: string
          accent_color: string
          background_color: string
          body_font: string
          cover_page_image_url: string | null
          cover_page_subtitle: string | null
          cover_page_title: string
          created_at: string
          font_size_base: number
          footer_height: number
          header_height: number
          heading_font: string
          id: string
          image_style: string
          include_about_page: boolean
          include_cover_page: boolean
          include_event_details: boolean
          include_investment_summary: boolean
          include_speaker_profiles: boolean
          include_thank_you_page: boolean
          layout_style: string
          line_height: number
          name: string
          page_contents: Json | null
          page_margin: number
          primary_color: string
          secondary_color: string
          section_spacing: number
          show_company_logo: boolean
          show_speaker_bios: boolean
          show_speaker_images: boolean
          show_speaker_rates: boolean
          speaker_layout: string
          text_color: string
          updated_at: string
          user_id: string
          watermark_text: string | null
        }
        Insert: {
          about_us_mission: string
          accent_color?: string
          background_color?: string
          body_font?: string
          cover_page_image_url?: string | null
          cover_page_subtitle?: string | null
          cover_page_title: string
          created_at?: string
          font_size_base?: number
          footer_height?: number
          header_height?: number
          heading_font?: string
          id?: string
          image_style?: string
          include_about_page?: boolean
          include_cover_page?: boolean
          include_event_details?: boolean
          include_investment_summary?: boolean
          include_speaker_profiles?: boolean
          include_thank_you_page?: boolean
          layout_style?: string
          line_height?: number
          name: string
          page_contents?: Json | null
          page_margin?: number
          primary_color?: string
          secondary_color?: string
          section_spacing?: number
          show_company_logo?: boolean
          show_speaker_bios?: boolean
          show_speaker_images?: boolean
          show_speaker_rates?: boolean
          speaker_layout?: string
          text_color?: string
          updated_at?: string
          user_id: string
          watermark_text?: string | null
        }
        Update: {
          about_us_mission?: string
          accent_color?: string
          background_color?: string
          body_font?: string
          cover_page_image_url?: string | null
          cover_page_subtitle?: string | null
          cover_page_title?: string
          created_at?: string
          font_size_base?: number
          footer_height?: number
          header_height?: number
          heading_font?: string
          id?: string
          image_style?: string
          include_about_page?: boolean
          include_cover_page?: boolean
          include_event_details?: boolean
          include_investment_summary?: boolean
          include_speaker_profiles?: boolean
          include_thank_you_page?: boolean
          layout_style?: string
          line_height?: number
          name?: string
          page_contents?: Json | null
          page_margin?: number
          primary_color?: string
          secondary_color?: string
          section_spacing?: number
          show_company_logo?: boolean
          show_speaker_bios?: boolean
          show_speaker_images?: boolean
          show_speaker_rates?: boolean
          speaker_layout?: string
          text_color?: string
          updated_at?: string
          user_id?: string
          watermark_text?: string | null
        }
        Relationships: []
      }
      proposals: {
        Row: {
          created_at: string
          details: Json | null
          event_name: string | null
          id: string
          pdf_path: string | null
          speaker_id: string | null
          status: string | null
          submitted_date: string | null
        }
        Insert: {
          created_at?: string
          details?: Json | null
          event_name?: string | null
          id?: string
          pdf_path?: string | null
          speaker_id?: string | null
          status?: string | null
          submitted_date?: string | null
        }
        Update: {
          created_at?: string
          details?: Json | null
          event_name?: string | null
          id?: string
          pdf_path?: string | null
          speaker_id?: string | null
          status?: string | null
          submitted_date?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "proposals_speaker_id_fkey"
            columns: ["speaker_id"]
            isOneToOne: false
            referencedRelation: "speakers"
            referencedColumns: ["id"]
          },
        ]
      }
      speakers: {
        Row: {
          availability: string | null
          bio: string | null
          category: string | null
          created_at: string
          date_of_birth: string | null
          experience: string | null
          id: string
          location: string | null
          name: string
          rate: number | null
          specialties: string[] | null
        }
        Insert: {
          availability?: string | null
          bio?: string | null
          category?: string | null
          created_at?: string
          date_of_birth?: string | null
          experience?: string | null
          id?: string
          location?: string | null
          name: string
          rate?: number | null
          specialties?: string[] | null
        }
        Update: {
          availability?: string | null
          bio?: string | null
          category?: string | null
          created_at?: string
          date_of_birth?: string | null
          experience?: string | null
          id?: string
          location?: string | null
          name?: string
          rate?: number | null
          specialties?: string[] | null
        }
        Relationships: []
      }
      specialties: {
        Row: {
          created_at: string
          id: string
          name: string
        }
        Insert: {
          created_at?: string
          id?: string
          name: string
        }
        Update: {
          created_at?: string
          id?: string
          name?: string
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      get_todays_birthdays: {
        Args: Record<PropertyKey, never>
        Returns: {
          id: string
          name: string
          email: string
          date_of_birth: string
        }[]
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const
