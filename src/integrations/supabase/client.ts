// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://bpqwisbseevdcjsfnbhz.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJwcXdpc2JzZWV2ZGNqc2ZuYmh6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk2NTg2NTgsImV4cCI6MjA2NTIzNDY1OH0.aOsNQWo0MiYKpGF_G84gZ_DPy5FRe_s2uVXob9micWU";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);