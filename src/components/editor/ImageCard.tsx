
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { X, Move, Settings } from 'lucide-react';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';

interface PageImage {
  id: string;
  url: string;
  position: 'top-left' | 'top-right' | 'center' | 'bottom-left' | 'bottom-right' | 'background';
  size: 'small' | 'medium' | 'large' | 'full';
  x?: number;
  y?: number;
}

interface ImageCardProps {
  image: PageImage;
  onUpdatePosition: (imageId: string, position: PageImage['position']) => void;
  onUpdateSize: (imageId: string, size: PageImage['size']) => void;
  onRemove: (imageId: string) => void;
  onDragStart: (imageId: string) => void;
}

export function ImageCard({ 
  image, 
  onUpdatePosition, 
  onUpdateSize, 
  onRemove, 
  onDragStart 
}: ImageCardProps) {
  const [isOpen, setIsOpen] = useState(false);

  const getPositionLabel = (position: PageImage['position']) => {
    const labels = {
      'top-left': 'Top Left',
      'top-right': 'Top Right',
      'center': 'Center',
      'bottom-left': 'Bottom Left',
      'bottom-right': 'Bottom Right',
      'background': 'Background'
    };
    return labels[position];
  };

  const getSizeLabel = (size: PageImage['size']) => {
    const labels = {
      'small': 'Small',
      'medium': 'Medium',
      'large': 'Large',
      'full': 'Full Width'
    };
    return labels[size];
  };

  return (
    <Card className="p-4 transition-all duration-200 hover:shadow-md border-l-4 border-l-blue-500">
      <div className="flex items-start gap-4">
        <div className="relative group">
          <img
            src={image.url}
            alt="Page image"
            className="w-20 h-20 object-cover rounded-lg border-2 border-gray-200 cursor-move transition-all duration-200 group-hover:border-blue-400 group-hover:shadow-lg"
            draggable
            onDragStart={() => onDragStart(image.id)}
          />
          <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 rounded-lg flex items-center justify-center transition-all duration-200">
            <Move className="w-5 h-5 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
          </div>
        </div>
        
        <div className="flex-1 space-y-3">
          <div className="flex items-center justify-between">
            <div className="flex gap-2">
              <Badge variant="secondary" className="text-xs">
                {getPositionLabel(image.position)}
              </Badge>
              <Badge variant="outline" className="text-xs">
                {getSizeLabel(image.size)}
              </Badge>
            </div>
            <div className="flex gap-2">
              <Collapsible open={isOpen} onOpenChange={setIsOpen}>
                <CollapsibleTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                    <Settings className="w-4 h-4" />
                  </Button>
                </CollapsibleTrigger>
                <CollapsibleContent className="mt-3">
                  <div className="grid grid-cols-2 gap-3">
                    <div>
                      <Label className="text-xs font-medium text-gray-600">Position</Label>
                      <Select 
                        value={image.position} 
                        onValueChange={(value) => onUpdatePosition(image.id, value as PageImage['position'])}
                      >
                        <SelectTrigger className="h-8 text-xs">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="top-left">Top Left</SelectItem>
                          <SelectItem value="top-right">Top Right</SelectItem>
                          <SelectItem value="center">Center</SelectItem>
                          <SelectItem value="bottom-left">Bottom Left</SelectItem>
                          <SelectItem value="bottom-right">Bottom Right</SelectItem>
                          <SelectItem value="background">Background</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div>
                      <Label className="text-xs font-medium text-gray-600">Size</Label>
                      <Select 
                        value={image.size} 
                        onValueChange={(value) => onUpdateSize(image.id, value as PageImage['size'])}
                      >
                        <SelectTrigger className="h-8 text-xs">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="small">Small</SelectItem>
                          <SelectItem value="medium">Medium</SelectItem>
                          <SelectItem value="large">Large</SelectItem>
                          <SelectItem value="full">Full Width</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </CollapsibleContent>
              </Collapsible>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onRemove(image.id)}
                className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
}
