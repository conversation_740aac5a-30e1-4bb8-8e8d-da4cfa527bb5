
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Upload, Loader2 } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

interface PageImage {
  id: string;
  url: string;
  position: 'top-left' | 'top-right' | 'center' | 'bottom-left' | 'bottom-right' | 'background';
  size: 'small' | 'medium' | 'large' | 'full';
  x?: number;
  y?: number;
}

interface ImageUploaderProps {
  onImageUploaded: (image: PageImage) => void;
  pageType: string;
}

export function ImageUploader({ onImageUploaded, pageType }: ImageUploaderProps) {
  const { toast } = useToast();
  const [isUploading, setIsUploading] = useState(false);

  const handleImageUpload = async (file: File) => {
    setIsUploading(true);
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Not authenticated');

      const filePath = `${user.id}/${Date.now()}_${file.name}`;
      const { error: uploadError } = await supabase.storage
        .from('template_images')
        .upload(filePath, file);
      
      if (uploadError) throw uploadError;

      const { data: { publicUrl } } = supabase.storage
        .from('template_images')
        .getPublicUrl(filePath);

      const newImage: PageImage = {
        id: Date.now().toString(),
        url: publicUrl,
        position: 'center',
        size: 'medium'
      };

      onImageUploaded(newImage);
      toast({ title: 'Image uploaded successfully!' });
    } catch (error: any) {
      toast({ title: 'Upload failed', description: error.message, variant: 'destructive' });
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className="space-y-2">
      <input
        type="file"
        accept="image/*"
        onChange={(e) => e.target.files?.[0] && handleImageUpload(e.target.files[0])}
        className="hidden"
        id={`image-upload-${pageType}`}
        disabled={isUploading}
      />
      <Button
        variant="outline"
        size="sm"
        onClick={() => document.getElementById(`image-upload-${pageType}`)?.click()}
        disabled={isUploading}
        className="w-full"
      >
        {isUploading ? (
          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
        ) : (
          <Upload className="w-4 h-4 mr-2" />
        )}
        {isUploading ? 'Uploading...' : 'Add Image'}
      </Button>
    </div>
  );
}
