
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { FileText, Type, AlignLeft } from 'lucide-react';

interface PageContent {
  title: string;
  subtitle?: string;
  content: string;
  images: Array<{
    id: string;
    url: string;
    position: 'top-left' | 'top-right' | 'center' | 'bottom-left' | 'bottom-right' | 'background';
    size: 'small' | 'medium' | 'large' | 'full';
  }>;
}

interface ContentEditorProps {
  pageType: string;
  content: PageContent;
  onContentChange: (content: PageContent) => void;
}

export function ContentEditor({ pageType, content, onContentChange }: ContentEditorProps) {
  return (
    <Card className="border border-muted-foreground/20 shadow-sm">
      <CardHeader className="pb-6">
        <CardTitle className="flex items-center gap-3 text-xl font-semibold text-foreground">
          <div className="p-2 rounded-md bg-blue-500/10">
            <FileText className="w-5 h-5 text-blue-600" />
          </div>
          <span>{pageType} Content</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-3">
          <Label htmlFor="title" className="flex items-center gap-2 text-base font-medium text-foreground">
            <Type className="w-4 h-4 text-muted-foreground" />
            Page Title
          </Label>
          <Input
            id="title"
            value={content.title}
            onChange={(e) => onContentChange({ ...content, title: e.target.value })}
            placeholder={`Enter ${pageType.toLowerCase()} title`}
            className="text-base py-3 border-muted-foreground/30 focus:border-primary transition-colors"
          />
        </div>

        <div className="space-y-3">
          <Label htmlFor="subtitle" className="flex items-center gap-2 text-base font-medium text-foreground">
            <Type className="w-4 h-4 text-muted-foreground" />
            Subtitle
            <span className="text-sm text-muted-foreground font-normal">(Optional)</span>
          </Label>
          <Input
            id="subtitle"
            value={content.subtitle || ''}
            onChange={(e) => onContentChange({ ...content, subtitle: e.target.value })}
            placeholder="Enter subtitle"
            className="text-base py-3 border-muted-foreground/30 focus:border-primary transition-colors"
          />
        </div>

        <div className="space-y-3">
          <Label htmlFor="content" className="flex items-center gap-2 text-base font-medium text-foreground">
            <AlignLeft className="w-4 h-4 text-muted-foreground" />
            Content
          </Label>
          <Textarea
            id="content"
            rows={8}
            value={content.content}
            onChange={(e) => onContentChange({ ...content, content: e.target.value })}
            placeholder={`Enter ${pageType.toLowerCase()} content`}
            className="text-base leading-relaxed border-muted-foreground/30 focus:border-primary transition-colors resize-none"
          />
          <p className="text-sm text-muted-foreground">
            {content.content.length} characters
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
