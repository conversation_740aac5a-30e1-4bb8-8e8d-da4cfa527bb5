import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Eye, Monitor, FileText } from 'lucide-react';

interface PageImage {
  id: string;
  url: string;
  position: 'top-left' | 'top-right' | 'center' | 'bottom-left' | 'bottom-right' | 'background';
  size: 'small' | 'medium' | 'large' | 'full';
  x?: number;
  y?: number;
}

interface PageContent {
  title: string;
  subtitle?: string;
  content: string;
  images: PageImage[];
}

interface PagePreviewProps {
  pageType: string;
  content: PageContent;
  templateSettings: any;
}

export function PagePreview({ pageType, content, templateSettings }: PagePreviewProps) {
  const getImageSizeClass = (size: PageImage['size']) => {
    switch (size) {
      case 'small': return 'w-24 h-24';
      case 'medium': return 'w-48 h-32';
      case 'large': return 'w-64 h-48';
      case 'full': return 'w-full h-48';
      default: return 'w-48 h-32';
    }
  };

  const getImagePositionStyles = (position: PageImage['position'], hasTitle: boolean, hasSubtitle: boolean, hasContent: boolean) => {
    const baseStyles = 'object-cover rounded-lg shadow-md';
    
    switch (position) {
      case 'top-left': 
        return {
          className: `${baseStyles} float-left`,
          style: { 
            marginRight: '20px', 
            marginBottom: hasTitle || hasSubtitle ? '16px' : '12px',
            marginTop: hasTitle ? '12px' : '0px'
          }
        };
      case 'top-right': 
        return {
          className: `${baseStyles} float-right`,
          style: { 
            marginLeft: '20px', 
            marginBottom: hasTitle || hasSubtitle ? '16px' : '12px',
            marginTop: hasTitle ? '12px' : '0px'
          }
        };
      case 'center': 
        return {
          className: `${baseStyles} block mx-auto`,
          style: { 
            marginTop: hasTitle || hasSubtitle ? '20px' : '12px',
            marginBottom: hasContent ? '20px' : '12px'
          }
        };
      case 'bottom-left': 
        return {
          className: `${baseStyles} float-left`,
          style: { 
            marginRight: '20px', 
            marginTop: hasContent ? '16px' : '12px'
          }
        };
      case 'bottom-right': 
        return {
          className: `${baseStyles} float-right`,
          style: { 
            marginLeft: '20px', 
            marginTop: hasContent ? '16px' : '12px'
          }
        };
      case 'background': 
        return {
          className: 'absolute inset-0 w-full h-full object-cover opacity-10 rounded-lg',
          style: {}
        };
      default: 
        return {
          className: `${baseStyles} block mx-auto`,
          style: { marginTop: '12px', marginBottom: '12px' }
        };
    }
  };

  const backgroundImage = content.images.find(img => img.position === 'background');
  const foregroundImages = content.images.filter(img => img.position !== 'background');

  const hasTitle = !!content.title;
  const hasSubtitle = !!content.subtitle;
  const hasContent = !!content.content;

  return (
    <Card className="h-full shadow-lg border border-muted-foreground/20 bg-background">
      <CardHeader className="pb-6">
        <CardTitle className="flex items-center justify-between text-xl font-semibold text-foreground">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-md bg-green-500/10">
              <Eye className="w-5 h-5 text-green-600" />
            </div>
            <span>{pageType} Preview</span>
          </div>
          <Badge variant="outline" className="flex items-center gap-1 text-sm">
            <Monitor className="w-3 h-3" />
            Live Preview
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="p-6">
        <div 
          className="relative min-h-96 p-8 border-2 border-dashed border-muted-foreground/30 rounded-xl bg-card overflow-hidden shadow-inner"
          style={{
            fontFamily: templateSettings?.body_font || 'system-ui, sans-serif',
            fontSize: `${templateSettings?.font_size_base || 14}px`,
            lineHeight: templateSettings?.line_height || 1.6,
            color: templateSettings?.text_color || 'hsl(var(--foreground))',
            backgroundColor: templateSettings?.background_color || 'hsl(var(--background))',
          }}
        >
          {/* Background Image */}
          {backgroundImage && (
            <div className="absolute inset-0 z-0">
              <img
                src={backgroundImage.url}
                alt="Background"
                className="w-full h-full object-cover opacity-10 rounded-lg"
              />
            </div>
          )}

          {/* Content with Smart Image Positioning */}
          <div className="relative z-10 space-y-6">
            {/* Title */}
            {hasTitle && (
              <h1
                className="font-bold leading-tight"
                style={{
                  fontFamily: templateSettings?.heading_font || 'system-ui, sans-serif',
                  color: templateSettings?.primary_color || 'hsl(var(--primary))',
                  fontSize: `${(templateSettings?.font_size_base || 14) + 12}px`,
                }}
              >
                {content.title}
              </h1>
            )}

            {/* Subtitle */}
            {hasSubtitle && (
              <h2
                className="leading-tight opacity-90"
                style={{
                  color: templateSettings?.secondary_color || 'hsl(var(--muted-foreground))',
                  fontSize: `${(templateSettings?.font_size_base || 14) + 6}px`,
                }}
              >
                {content.subtitle}
              </h2>
            )}

            {/* Images positioned around content with smart spacing */}
            <div className="relative">
              {foregroundImages.map((image) => {
                const positionData = getImagePositionStyles(image.position, hasTitle, hasSubtitle, hasContent);
                return (
                  <img
                    key={image.id}
                    src={image.url}
                    alt={`${pageType} image`}
                    className={`${getImageSizeClass(image.size)} ${positionData.className}`}
                    style={{
                      borderRadius: templateSettings?.image_style === 'circle' ? '50%' : 
                                   templateSettings?.image_style === 'rounded' ? '12px' : '4px',
                      ...positionData.style
                    }}
                  />
                );
              })}

              {/* Content with proper text flow around images */}
              {hasContent && (
                <div 
                  className="leading-relaxed whitespace-pre-wrap"
                  style={{ 
                    marginTop: foregroundImages.length > 0 ? '20px' : '0px',
                    textAlign: foregroundImages.some(img => img.position.includes('left') || img.position.includes('right')) ? 'justify' : 'left',
                    color: 'hsl(var(--foreground))',
                  }}
                >
                  {content.content}
                </div>
              )}
            </div>
          </div>

          {/* Watermark */}
          {templateSettings?.watermark_text && (
            <div className="absolute bottom-6 right-6 text-xs opacity-20 rotate-45 pointer-events-none text-muted-foreground">
              {templateSettings.watermark_text}
            </div>
          )}

          {/* Empty state */}
          {!hasTitle && !hasSubtitle && !hasContent && content.images.length === 0 && (
            <div className="flex items-center justify-center h-full text-center">
              <div className="space-y-3">
                <div className="w-16 h-16 mx-auto rounded-full bg-muted flex items-center justify-center">
                  <FileText className="w-8 h-8 text-muted-foreground" />
                </div>
                <p className="text-muted-foreground">Start adding content to see the preview</p>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
