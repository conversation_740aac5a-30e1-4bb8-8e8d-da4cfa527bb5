import React from "react";
import { Proposal } from "../types/proposal";

export const ProposalPDFTemplate: React.FC<{ proposal: Proposal }> = ({
  proposal,
}) => {
  const theme = {
    colors: {
      primary: "#0D47A1",
      secondary: "#1976D2",
      accent: "#FFC107",
      textPrimary: "#212121",
      textSecondary: "#757575",
      background: "#FFFFFF",
      lightGray: "#F5F5F5",
    },
    fonts: {
      sans: "Arial, Helvetica, sans-serif",
    },
  };

  const pageStyle: React.CSSProperties = {
    width: "210mm",
    height: "297mm",
    margin: 0,
    padding: "20mm",
    backgroundColor: theme.colors.background,
    boxSizing: "border-box",
    fontFamily: theme.fonts.sans,
    color: theme.colors.textPrimary,
    position: "relative",
    display: "flex",
    flexDirection: "column",
  };

  const footerStyle: React.CSSProperties = {
    position: "absolute",
    bottom: "10mm",
    left: "20mm",
    right: "20mm",
    fontSize: "10px",
    color: theme.colors.textSecondary,
    display: "flex",
    justifyContent: "space-between",
    borderTop: `1px solid #E0E0E0`,
    paddingTop: "5mm",
  };

  const h1Style: React.CSSProperties = {
    fontSize: "36px",
    fontWeight: "normal",
    color: theme.colors.primary,
    margin: "0 0 10px 0",
    fontFamily: theme.fonts.sans,
  };

  const h2Style: React.CSSProperties = {
    fontSize: "24px",
    fontWeight: "bold",
    color: theme.colors.secondary,
    margin: "0 0 15px 0",
    borderBottom: `1px solid #E0E0E0`,
    paddingBottom: "8px",
  };

  const sideImageStyle: React.CSSProperties = {
    position: "absolute",
    left: "0",
    top: "0",
    width: "30mm",
    height: "100%",
    backgroundColor: theme.colors.primary,
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    padding: "10mm 0",
  };

  const mainContentStyle: React.CSSProperties = {
    marginLeft: "35mm",
    flex: 1,
  };

  const pageDividerStyle: React.CSSProperties = {
    width: "210mm",
    height: "297mm",
    margin: 0,
    padding: 0,
    backgroundImage: "url(/public/images/background.jpeg)",
    backgroundSize: "cover",
    backgroundPosition: "center",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    position: "relative",
  };

  const overlayStyle: React.CSSProperties = {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "rgba(0,0,0,0.6)",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
  };

  const dividerTitleStyle: React.CSSProperties = {
    color: "white",
    fontSize: "48px",
    fontWeight: "bold",
    textAlign: "center",
    zIndex: 1,
  };

  const totalPages = 8 + proposal.speakers.length;
  let currentPage = 0;

  return (
    <div style={{ fontFamily: theme.fonts.sans, backgroundColor: "white" }}>
      {/* PAGE 1: COVER PAGE */}
      <div
        className="pdf-page"
        style={{
          ...pageStyle,
          justifyContent: "center",
          alignItems: "center",
          textAlign: "center",
        }}
      >
        <div
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            width: "8mm",
            height: "100%",
            backgroundColor: theme.colors.primary,
          }}
        ></div>
        <div
          style={{
            position: "absolute",
            bottom: 0,
            right: 0,
            width: "50%",
            height: "8mm",
            backgroundColor: theme.colors.secondary,
          }}
        ></div>

        <div
          style={{
            border: `2px solid ${theme.colors.primary}`,
            padding: "40px",
            width: "80%",
          }}
        >
          <img
            src="/public/images/logo.png"
            alt="Logo"
            style={{
              width: "60mm",
              height: "auto",
              marginBottom: "30px",
              objectFit: "contain",
            }}
          />
          <p
            style={{
              fontSize: "18px",
              color: theme.colors.textSecondary,
              letterSpacing: "2px",
              textTransform: "uppercase",
              marginBottom: "20px",
              fontWeight: "normal",
              fontFamily: theme.fonts.sans,
            }}
          >
            Speaker Proposal
          </p>
          <h1 style={{ ...h1Style, fontSize: "48px", margin: "0 0 20px 0" }}>
            {proposal.event.eventName}
          </h1>
          <p
            style={{
              fontSize: "20px",
              marginBottom: "40px",
              fontWeight: "normal",
              fontFamily: theme.fonts.sans,
            }}
          >
            Prepared for:{" "}
            <span style={{ color: theme.colors.primary, fontWeight: "normal" }}>
              {proposal.clientName}
            </span>
          </p>
          <p
            style={{
              fontSize: "16px",
              color: theme.colors.textSecondary,
              fontWeight: "normal",
              fontFamily: theme.fonts.sans,
            }}
          >
            {new Date(proposal.createdAt).toLocaleDateString("en-US", {
              year: "numeric",
              month: "long",
              day: "numeric",
            })}
          </p>
        </div>
        <div style={footerStyle}>
          <span>Speaker Agency | Professional Proposals</span>
          <span>
            Page {++currentPage} of {totalPages}
          </span>
        </div>
      </div>

      {/* PAGE 2: ABOUT US */}
      <div className="pdf-page" style={pageStyle}>
        <div style={sideImageStyle}>
          <div
            style={{
              width: "20mm",
              height: "25mm",
              backgroundColor: "white",
              marginBottom: "10mm",
              borderRadius: "4px",
            }}
          ></div>
          <div
            style={{
              width: "20mm",
              height: "25mm",
              backgroundColor: "white",
              marginBottom: "10mm",
              borderRadius: "4px",
            }}
          ></div>
          <div
            style={{
              width: "20mm",
              height: "25mm",
              backgroundColor: "white",
              marginBottom: "10mm",
              borderRadius: "4px",
            }}
          ></div>
          <div
            style={{
              width: "20mm",
              height: "25mm",
              backgroundColor: "white",
              marginBottom: "10mm",
              borderRadius: "4px",
            }}
          ></div>
          <div
            style={{
              width: "20mm",
              height: "25mm",
              backgroundColor: "white",
              marginBottom: "10mm",
              borderRadius: "4px",
            }}
          ></div>
        </div>

        <div style={mainContentStyle}>
          <h1 style={h1Style}>About Speaker Agency</h1>
          <h2 style={h2Style}>Our Mission</h2>
          <p
            style={{
              fontSize: "16px",
              color: theme.colors.textSecondary,
              lineHeight: 1.6,
              marginBottom: "30px",
            }}
          >
            At Speaker Agency, we connect world-class speakers with
            organizations seeking exceptional thought leadership and
            inspiration. Our mission is to deliver transformative experiences
            that educate, motivate, and drive meaningful change.
          </p>

          <h2 style={h2Style}>Why Choose Us</h2>
          <div
            style={{
              display: "grid",
              gridTemplateColumns: "1fr 1fr",
              gap: "20px",
              marginBottom: "30px",
            }}
          >
            <div>
              <h3 style={{ color: theme.colors.primary, marginBottom: "10px" }}>
                Expert Curation
              </h3>
              <p
                style={{
                  fontSize: "14px",
                  color: theme.colors.textSecondary,
                  lineHeight: 1.6,
                }}
              >
                We carefully select speakers who align with your event goals and
                audience needs.
              </p>
            </div>
            <div>
              <h3 style={{ color: theme.colors.primary, marginBottom: "10px" }}>
                Seamless Experience
              </h3>
              <p
                style={{
                  fontSize: "14px",
                  color: theme.colors.textSecondary,
                  lineHeight: 1.6,
                }}
              >
                From booking to event day, we handle all logistics for a
                stress-free experience.
              </p>
            </div>
          </div>

          <div style={{ display: "flex", gap: "20px", marginTop: "auto" }}>
            <div
              style={{
                flex: 1,
                backgroundColor: theme.colors.lightGray,
                padding: "20px",
                borderRadius: "4px",
                textAlign: "center",
              }}
            >
              <div
                style={{
                  fontSize: "48px",
                  fontWeight: "bold",
                  color: theme.colors.primary,
                  marginBottom: "8px",
                }}
              >
                500+
              </div>
              <div style={{ color: theme.colors.textSecondary }}>
                Expert Speakers
              </div>
            </div>
            <div
              style={{
                flex: 1,
                backgroundColor: theme.colors.lightGray,
                padding: "20px",
                borderRadius: "4px",
                textAlign: "center",
              }}
            >
              <div
                style={{
                  fontSize: "48px",
                  fontWeight: "bold",
                  color: theme.colors.primary,
                  marginBottom: "8px",
                }}
              >
                1000+
              </div>
              <div style={{ color: theme.colors.textSecondary }}>
                Successful Events
              </div>
            </div>
            <div
              style={{
                flex: 1,
                backgroundColor: theme.colors.lightGray,
                padding: "20px",
                borderRadius: "4px",
                textAlign: "center",
              }}
            >
              <div
                style={{
                  fontSize: "48px",
                  fontWeight: "bold",
                  color: theme.colors.primary,
                  marginBottom: "8px",
                }}
              >
                98%
              </div>
              <div style={{ color: theme.colors.textSecondary }}>
                Client Satisfaction
              </div>
            </div>
          </div>
        </div>

        <div style={footerStyle}>
          <span>Speaker Agency | About Us</span>
          <span>
            Page {++currentPage} of {totalPages}
          </span>
        </div>
      </div>

      {/* PAGE 3: PARTNERS */}
      <div className="pdf-page" style={pageStyle}>
        <div style={sideImageStyle}>
          <div
            style={{
              width: "20mm",
              height: "25mm",
              backgroundColor: "white",
              marginBottom: "10mm",
              borderRadius: "4px",
            }}
          ></div>
          <div
            style={{
              width: "20mm",
              height: "25mm",
              backgroundColor: "white",
              marginBottom: "10mm",
              borderRadius: "4px",
            }}
          ></div>
          <div
            style={{
              width: "20mm",
              height: "25mm",
              backgroundColor: "white",
              marginBottom: "10mm",
              borderRadius: "4px",
            }}
          ></div>
          <div
            style={{
              width: "20mm",
              height: "25mm",
              backgroundColor: "white",
              marginBottom: "10mm",
              borderRadius: "4px",
            }}
          ></div>
          <div
            style={{
              width: "20mm",
              height: "25mm",
              backgroundColor: "white",
              marginBottom: "10mm",
              borderRadius: "4px",
            }}
          ></div>
        </div>

        <div style={mainContentStyle}>
          <h1 style={h1Style}>Our Partners</h1>
          <h2 style={h2Style}>Trusted Collaborations</h2>
          <p
            style={{
              fontSize: "16px",
              color: theme.colors.textSecondary,
              lineHeight: 1.6,
              marginBottom: "30px",
            }}
          >
            We work with leading organizations and institutions to deliver
            exceptional events and create meaningful impact across various
            industries and sectors.
          </p>

          <div
            style={{
              display: "grid",
              gridTemplateColumns: "1fr 1fr",
              gap: "20px",
              marginBottom: "30px",
            }}
          >
            <div
              style={{
                padding: "20px",
                border: `1px solid ${theme.colors.primary}`,
                borderRadius: "4px",
                textAlign: "center",
              }}
            >
              <h3 style={{ color: theme.colors.primary, marginBottom: "10px" }}>
                Corporate Partners
              </h3>
              <p
                style={{
                  fontSize: "14px",
                  color: theme.colors.textSecondary,
                  lineHeight: 1.6,
                }}
              >
                Fortune 500 companies, tech startups, and industry leaders trust
                us with their most important events.
              </p>
            </div>
            <div
              style={{
                padding: "20px",
                border: `1px solid ${theme.colors.secondary}`,
                borderRadius: "4px",
                textAlign: "center",
              }}
            >
              <h3
                style={{ color: theme.colors.secondary, marginBottom: "10px" }}
              >
                Educational Institutions
              </h3>
              <p
                style={{
                  fontSize: "14px",
                  color: theme.colors.textSecondary,
                  lineHeight: 1.6,
                }}
              >
                Universities, research centers, and educational organizations
                partner with us for academic excellence.
              </p>
            </div>
          </div>

          <div
            style={{
              backgroundColor: theme.colors.lightGray,
              padding: "20px",
              borderRadius: "4px",
              textAlign: "center",
            }}
          >
            <h3 style={{ color: theme.colors.primary, marginBottom: "15px" }}>
              Partner Logos
            </h3>
            <p style={{ fontSize: "14px", color: theme.colors.textSecondary }}>
              [Partner logos will be added here]
            </p>
          </div>
        </div>

        <div style={footerStyle}>
          <span>Speaker Agency | Our Partners</span>
          <span>
            Page {++currentPage} of {totalPages}
          </span>
        </div>
      </div>

      {/* PAGE 4: EVENT OVERVIEW */}
      <div className="pdf-page" style={pageStyle}>
        <div style={sideImageStyle}>
          <div
            style={{
              width: "20mm",
              height: "25mm",
              backgroundColor: "white",
              marginBottom: "10mm",
              borderRadius: "4px",
            }}
          ></div>
          <div
            style={{
              width: "20mm",
              height: "25mm",
              backgroundColor: "white",
              marginBottom: "10mm",
              borderRadius: "4px",
            }}
          ></div>
          <div
            style={{
              width: "20mm",
              height: "25mm",
              backgroundColor: "white",
              marginBottom: "10mm",
              borderRadius: "4px",
            }}
          ></div>
          <div
            style={{
              width: "20mm",
              height: "25mm",
              backgroundColor: "white",
              marginBottom: "10mm",
              borderRadius: "4px",
            }}
          ></div>
          <div
            style={{
              width: "20mm",
              height: "25mm",
              backgroundColor: "white",
              marginBottom: "10mm",
              borderRadius: "4px",
            }}
          ></div>
        </div>

        <div style={mainContentStyle}>
          <h1 style={h1Style}>Event Overview</h1>
          <h2 style={h2Style}>{proposal.event.eventName}</h2>
          <p
            style={{
              fontSize: "16px",
              color: theme.colors.textSecondary,
              lineHeight: 1.6,
              marginBottom: "30px",
            }}
          >
            {proposal.event.description}
          </p>
          <div
            style={{
              border: `1px solid #E0E0E0`,
              borderRadius: "4px",
              padding: "20px",
            }}
          >
            <div
              style={{
                display: "grid",
                gridTemplateColumns: "1fr 1fr",
                gap: "20px 40px",
              }}
            >
              <div>
                <strong>Date:</strong>{" "}
                {new Date(proposal.event.eventDate).toLocaleDateString()}
              </div>
              <div>
                <strong>Location:</strong> {proposal.event.eventLocation}
              </div>
              <div>
                <strong>Event Type:</strong> {proposal.event.eventType}
              </div>
              <div>
                <strong>Audience Size:</strong> {proposal.event.audience}
              </div>
              <div>
                <strong>Budget Range:</strong> {proposal.event.budget}
              </div>
            </div>
          </div>
        </div>

        <div style={footerStyle}>
          <span>{proposal.event.eventName} | Event Overview</span>
          <span>
            Page {++currentPage} of {totalPages}
          </span>
        </div>
      </div>

      {/* PAGE DIVIDER: SPEAKERS */}
      <div className="pdf-page" style={pageDividerStyle}>
        <div style={overlayStyle}>
          <h1 style={dividerTitleStyle}>Speakers</h1>
        </div>
      </div>

      {/* SPEAKERS PAGES (One page per speaker) */}
      {proposal.speakers.map(({ speaker, role, notes }, index) => (
        <div key={speaker.id} className="pdf-page" style={pageStyle}>
          <div style={sideImageStyle}>
            <div
              style={{
                width: "20mm",
                height: "25mm",
                backgroundColor: "white",
                marginBottom: "10mm",
                borderRadius: "4px",
              }}
            ></div>
            <div
              style={{
                width: "20mm",
                height: "25mm",
                backgroundColor: "white",
                marginBottom: "10mm",
                borderRadius: "4px",
              }}
            ></div>
            <div
              style={{
                width: "20mm",
                height: "25mm",
                backgroundColor: "white",
                marginBottom: "10mm",
                borderRadius: "4px",
              }}
            ></div>
            <div
              style={{
                width: "20mm",
                height: "25mm",
                backgroundColor: "white",
                marginBottom: "10mm",
                borderRadius: "4px",
              }}
            ></div>
            <div
              style={{
                width: "20mm",
                height: "25mm",
                backgroundColor: "white",
                marginBottom: "10mm",
                borderRadius: "4px",
              }}
            ></div>
          </div>

          <div style={mainContentStyle}>
            <div style={{ display: "flex", gap: "20mm", flex: 1 }}>
              <div style={{ flex: 1 }}>
                <h1 style={{ ...h1Style, marginBottom: "20px" }}>
                  {speaker.name}
                </h1>
                <h2 style={{ ...h2Style, marginTop: 0 }}>Biography</h2>
                <p
                  style={{
                    fontSize: "14px",
                    color: theme.colors.textSecondary,
                    lineHeight: 1.6,
                    marginBottom: "20px",
                  }}
                >
                  {speaker.bio || "No biography available"}
                </p>
                <h2 style={h2Style}>Key Topics</h2>
                <ul
                  style={{
                    paddingLeft: "20px",
                    margin: "0 0 20px 0",
                    columnCount: 2,
                    columnGap: "20px",
                  }}
                >
                  {(speaker.specialties || ["General Speaking"])
                    .slice(0, 6)
                    .map((specialty, idx) => (
                      <li
                        key={idx}
                        style={{
                          marginBottom: "8px",
                          color: theme.colors.textSecondary,
                          fontSize: "14px",
                        }}
                      >
                        {specialty}
                      </li>
                    ))}
                </ul>
                {notes && (
                  <div
                    style={{
                      marginTop: "20px",
                      backgroundColor: "#FFF8E1",
                      padding: "15px",
                      borderRadius: "4px",
                      borderLeft: `4px solid ${theme.colors.accent}`,
                    }}
                  >
                    <h4
                      style={{
                        fontWeight: "bold",
                        marginBottom: "8px",
                        color: theme.colors.textPrimary,
                        marginTop: 0,
                      }}
                    >
                      Notes for this event:
                    </h4>
                    <p
                      style={{
                        color: theme.colors.textSecondary,
                        fontSize: "14px",
                        margin: 0,
                      }}
                    >
                      {notes}
                    </p>
                  </div>
                )}
              </div>
              <div
                style={{ flexShrink: 0, width: "60mm", textAlign: "center" }}
              >
                {speaker.image ? (
                  <img
                    src={speaker.image}
                    alt={speaker.name}
                    style={{
                      width: "100%",
                      height: "auto",
                      aspectRatio: "1 / 1",
                      borderRadius: "50%",
                      objectFit: "cover",
                      marginBottom: "20px",
                      border: `4px solid ${theme.colors.primary}`,
                    }}
                  />
                ) : (
                  <div
                    style={{
                      width: "100%",
                      aspectRatio: "1 / 1",
                      borderRadius: "50%",
                      backgroundColor: theme.colors.lightGray,
                      border: `4px solid ${theme.colors.primary}`,
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      marginBottom: "20px",
                      fontSize: "14px",
                      color: theme.colors.textSecondary,
                    }}
                  >
                    No Image
                  </div>
                )}
                <p
                  style={{
                    color: theme.colors.textSecondary,
                    marginBottom: "20px",
                  }}
                >
                  {speaker.category || "Speaker"}
                </p>
                <div
                  style={{
                    backgroundColor: theme.colors.secondary,
                    color: "white",
                    padding: "15px",
                    borderRadius: "4px",
                  }}
                >
                  <div style={{ fontSize: "28px", fontWeight: "bold" }}>
                    ${(speaker.rate || 0).toLocaleString()}
                  </div>
                  <div style={{ fontSize: "12px", textTransform: "uppercase" }}>
                    Speaking Fee
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div style={footerStyle}>
            <span>{proposal.event.eventName} | Speaker Profile</span>
            <span>
              Page {++currentPage} of {totalPages}
            </span>
          </div>
        </div>
      ))}

      {/* PAGE DIVIDER: CASE STUDY */}
      <div className="pdf-page" style={pageDividerStyle}>
        <div style={overlayStyle}>
          <h1 style={dividerTitleStyle}>Case Study</h1>
        </div>
      </div>

      {/* CASE STUDY PAGE 1: Misk Global Forum */}
      <div className="pdf-page" style={pageStyle}>
        <div style={sideImageStyle}>
          <div
            style={{
              width: "20mm",
              height: "25mm",
              backgroundColor: "white",
              marginBottom: "10mm",
              borderRadius: "4px",
            }}
          ></div>
          <div
            style={{
              width: "20mm",
              height: "25mm",
              backgroundColor: "white",
              marginBottom: "10mm",
              borderRadius: "4px",
            }}
          ></div>
          <div
            style={{
              width: "20mm",
              height: "25mm",
              backgroundColor: "white",
              marginBottom: "10mm",
              borderRadius: "4px",
            }}
          ></div>
          <div
            style={{
              width: "20mm",
              height: "25mm",
              backgroundColor: "white",
              marginBottom: "10mm",
              borderRadius: "4px",
            }}
          ></div>
          <div
            style={{
              width: "20mm",
              height: "25mm",
              backgroundColor: "white",
              marginBottom: "10mm",
              borderRadius: "4px",
            }}
          ></div>
        </div>

        <div style={mainContentStyle}>
          <h1 style={h1Style}>Case Study</h1>
          <h2 style={h2Style}>The Misk Global Forum (MGF)</h2>
          <p
            style={{
              fontSize: "16px",
              color: theme.colors.textSecondary,
              lineHeight: 1.6,
              marginBottom: "30px",
            }}
          >
            The Misk Global Forum (MGF) is the biggest youth forum that brings
            together young leaders, entrepreneurs, and innovators from around
            the world. MENA Speakers played a crucial role in securing
            high-profile speakers like Bill Gates and Steven Bartlett for MGF24.
          </p>

          <h2 style={h2Style}>Results</h2>
          <ul style={{ paddingLeft: "20px", margin: "0 0 20px 0" }}>
            <li
              style={{
                marginBottom: "10px",
                fontSize: "14px",
                color: theme.colors.textSecondary,
              }}
            >
              Speaker Sourcing & Selection
            </li>
            <li
              style={{
                marginBottom: "10px",
                fontSize: "14px",
                color: theme.colors.textSecondary,
              }}
            >
              Contract Negotiations & Logistics
            </li>
            <li
              style={{
                marginBottom: "10px",
                fontSize: "14px",
                color: theme.colors.textSecondary,
              }}
            >
              Content Preparation
            </li>
            <li
              style={{
                marginBottom: "10px",
                fontSize: "14px",
                color: theme.colors.textSecondary,
              }}
            >
              On-Site Speaker Management
            </li>
          </ul>
        </div>

        <div style={footerStyle}>
          <span>Speaker Agency | Case Study - MGF</span>
          <span>
            Page {++currentPage} of {totalPages}
          </span>
        </div>
      </div>

      {/* CASE STUDY PAGE 2: Experience MISK */}
      <div className="pdf-page" style={pageStyle}>
        <div style={sideImageStyle}>
          <div
            style={{
              width: "20mm",
              height: "25mm",
              backgroundColor: "white",
              marginBottom: "10mm",
              borderRadius: "4px",
            }}
          ></div>
          <div
            style={{
              width: "20mm",
              height: "25mm",
              backgroundColor: "white",
              marginBottom: "10mm",
              borderRadius: "4px",
            }}
          ></div>
          <div
            style={{
              width: "20mm",
              height: "25mm",
              backgroundColor: "white",
              marginBottom: "10mm",
              borderRadius: "4px",
            }}
          ></div>
          <div
            style={{
              width: "20mm",
              height: "25mm",
              backgroundColor: "white",
              marginBottom: "10mm",
              borderRadius: "4px",
            }}
          ></div>
          <div
            style={{
              width: "20mm",
              height: "25mm",
              backgroundColor: "white",
              marginBottom: "10mm",
              borderRadius: "4px",
            }}
          ></div>
        </div>

        <div style={mainContentStyle}>
          <h1 style={h1Style}>Case Study</h1>
          <h2 style={h2Style}>Experience MISK</h2>
          <p
            style={{
              fontSize: "16px",
              color: theme.colors.textSecondary,
              lineHeight: 1.6,
              marginBottom: "30px",
            }}
          >
            Experience Misk, held in February 2024 at Mohammed Bin Salman
            Non-profit City offered Misk Programs' beneficiaries a platform to
            celebrate achievements and explore opportunities. This annual event
            highlighted various educational and career stages, allowing
            attendees to engage with programs designed to inspire creativity and
            ambition.
          </p>

          <h2 style={h2Style}>Results</h2>
          <ul style={{ paddingLeft: "20px", margin: "0 0 20px 0" }}>
            <li
              style={{
                marginBottom: "10px",
                fontSize: "14px",
                color: theme.colors.textSecondary,
              }}
            >
              Hosted at Mohammed Bin Salman Non-profit City.
            </li>
            <li
              style={{
                marginBottom: "10px",
                fontSize: "14px",
                color: theme.colors.textSecondary,
              }}
            >
              Engaged over 100+ speakers, uniting a diverse mix of voices and
              perspectives.
            </li>
            <li
              style={{
                marginBottom: "10px",
                fontSize: "14px",
                color: theme.colors.textSecondary,
              }}
            >
              Conducted 22 stage sessions tailored to various aspects of
              education and career development.
            </li>
            <li
              style={{
                marginBottom: "10px",
                fontSize: "14px",
                color: theme.colors.textSecondary,
              }}
            >
              Organized more than 30 workshops, focusing on entrepreneurship,
              practical skills, and leadership.
            </li>
            <li
              style={{
                marginBottom: "10px",
                fontSize: "14px",
                color: theme.colors.textSecondary,
              }}
            >
              Created an environment for self-discovery and growth, encouraging
              attendees to explore and evolve.
            </li>
          </ul>
        </div>

        <div style={footerStyle}>
          <span>Speaker Agency | Case Study - Experience MISK</span>
          <span>
            Page {++currentPage} of {totalPages}
          </span>
        </div>
      </div>

      {/* CASE STUDY PAGE 3: Saudi African Youth Forum */}
      <div className="pdf-page" style={pageStyle}>
        <div style={sideImageStyle}>
          <div
            style={{
              width: "20mm",
              height: "25mm",
              backgroundColor: "white",
              marginBottom: "10mm",
              borderRadius: "4px",
            }}
          ></div>
          <div
            style={{
              width: "20mm",
              height: "25mm",
              backgroundColor: "white",
              marginBottom: "10mm",
              borderRadius: "4px",
            }}
          ></div>
          <div
            style={{
              width: "20mm",
              height: "25mm",
              backgroundColor: "white",
              marginBottom: "10mm",
              borderRadius: "4px",
            }}
          ></div>
          <div
            style={{
              width: "20mm",
              height: "25mm",
              backgroundColor: "white",
              marginBottom: "10mm",
              borderRadius: "4px",
            }}
          ></div>
          <div
            style={{
              width: "20mm",
              height: "25mm",
              backgroundColor: "white",
              marginBottom: "10mm",
              borderRadius: "4px",
            }}
          ></div>
        </div>

        <div style={mainContentStyle}>
          <h1 style={h1Style}>Case Study</h1>
          <h2 style={h2Style}>Saudi African Youth Forum '23</h2>
          <p
            style={{
              fontSize: "16px",
              color: theme.colors.textSecondary,
              lineHeight: 1.6,
              marginBottom: "30px",
            }}
          >
            The Saudi African Youth Forum, organized by Misk Foundation, aimed
            to exchange expertise across various fields including
            Entrepreneurship, Economy, Investment, Youth Health and Wellness,
            Arts and Culture, and the third sector. It served as a catalyst for
            strengthening bilateral relations. We gathered{" "}
            <strong>30+ speakers and moderators</strong>, with 9 programmed
            panel discussions, keynotes and workshops over 1 day.
          </p>

          <h2 style={h2Style}>Results</h2>
          <ul style={{ paddingLeft: "20px", margin: "0 0 20px 0" }}>
            <li
              style={{
                marginBottom: "10px",
                fontSize: "14px",
                color: theme.colors.textSecondary,
              }}
            >
              Established a platform for cooperation and knowledge exchange
              across science, education, technology, economy, culture, and art.
            </li>
            <li
              style={{
                marginBottom: "10px",
                fontSize: "14px",
                color: theme.colors.textSecondary,
              }}
            >
              Hosted in the "dynamic and innovative City Hub within Mohammed bin
              Salman non-profit city in Riyadh."
            </li>
            <li
              style={{
                marginBottom: "10px",
                fontSize: "14px",
                color: theme.colors.textSecondary,
              }}
            >
              Featured "engaging presentations and talks by over 25 Saudi and
              African expert speakers."
            </li>
            <li
              style={{
                marginBottom: "10px",
                fontSize: "14px",
                color: theme.colors.textSecondary,
              }}
            >
              Covered a "wide range of topics including culture and arts,
              entrepreneurship, social development, and youth health and
              wellbeing."
            </li>
            <li
              style={{
                marginBottom: "10px",
                fontSize: "14px",
                color: theme.colors.textSecondary,
              }}
            >
              Conducted "interactive sessions for hands-on experience, skill
              development, and peer collaboration."
            </li>
            <li
              style={{
                marginBottom: "10px",
                fontSize: "14px",
                color: theme.colors.textSecondary,
              }}
            >
              Offered "innovative and immersive experiences, adding a
              distinctive flair to the event."
            </li>
            <li
              style={{
                marginBottom: "10px",
                fontSize: "14px",
                color: theme.colors.textSecondary,
              }}
            >
              Provided "fun and networking opportunities with a delightful
              selection of refreshments, enhancing the overall experience."
            </li>
          </ul>
        </div>

        <div style={footerStyle}>
          <span>Speaker Agency | Case Study - Saudi African Youth Forum</span>
          <span>
            Page {++currentPage} of {totalPages}
          </span>
        </div>
      </div>

      {/* CASE STUDY PAGE 4: Mawhiba */}
      <div className="pdf-page" style={pageStyle}>
        <div style={sideImageStyle}>
          <div
            style={{
              width: "20mm",
              height: "25mm",
              backgroundColor: "white",
              marginBottom: "10mm",
              borderRadius: "4px",
            }}
          ></div>
          <div
            style={{
              width: "20mm",
              height: "25mm",
              backgroundColor: "white",
              marginBottom: "10mm",
              borderRadius: "4px",
            }}
          ></div>
          <div
            style={{
              width: "20mm",
              height: "25mm",
              backgroundColor: "white",
              marginBottom: "10mm",
              borderRadius: "4px",
            }}
          ></div>
          <div
            style={{
              width: "20mm",
              height: "25mm",
              backgroundColor: "white",
              marginBottom: "10mm",
              borderRadius: "4px",
            }}
          ></div>
          <div
            style={{
              width: "20mm",
              height: "25mm",
              backgroundColor: "white",
              marginBottom: "10mm",
              borderRadius: "4px",
            }}
          ></div>
        </div>

        <div style={mainContentStyle}>
          <h1 style={h1Style}>Case Study</h1>
          <h2 style={h2Style}>Mawhiba</h2>
          <p
            style={{
              fontSize: "16px",
              color: theme.colors.textSecondary,
              lineHeight: 1.6,
              marginBottom: "30px",
            }}
          >
            The Global Conference for Giftedness and Creativity 2024, themed
            "Beyond Creative Minds," highlighted Saudi Arabia's dedication to
            investing in talent and fostering innovation. This event brought
            together experts, innovators, and gifted individuals for discussions
            and workshops.
          </p>

          <h2 style={h2Style}>Results</h2>
          <ul style={{ paddingLeft: "20px", margin: "0 0 20px 0" }}>
            <li
              style={{
                marginBottom: "10px",
                fontSize: "14px",
                color: theme.colors.textSecondary,
              }}
            >
              Speakers, MC and Moderators, Creathon Facilitators sourcing.
            </li>
            <li
              style={{
                marginBottom: "10px",
                fontSize: "14px",
                color: theme.colors.textSecondary,
              }}
            >
              VIP Protocol and Managing High level Round Table Discussion.
            </li>
            <li
              style={{
                marginBottom: "10px",
                fontSize: "14px",
                color: theme.colors.textSecondary,
              }}
            >
              Managing and Coordinating with Creathon Participants.
            </li>
            <li
              style={{
                marginBottom: "10px",
                fontSize: "14px",
                color: theme.colors.textSecondary,
              }}
            >
              Speakers contingency plan.
            </li>
            <li
              style={{
                marginBottom: "10px",
                fontSize: "14px",
                color: theme.colors.textSecondary,
              }}
            >
              Speakers communication: Official invitation, session and logistics
              briefs, alignment on talk/session/panel topic and talking points.
            </li>
            <li
              style={{
                marginBottom: "10px",
                fontSize: "14px",
                color: theme.colors.textSecondary,
              }}
            >
              Collection of marketing assets: Title, Headshot, bio, etc.
            </li>
            <li
              style={{
                marginBottom: "10px",
                fontSize: "14px",
                color: theme.colors.textSecondary,
              }}
            >
              MC script development.
            </li>
            <li
              style={{
                marginBottom: "10px",
                fontSize: "14px",
                color: theme.colors.textSecondary,
              }}
            >
              Alignment with moderators on questions.
            </li>
            <li
              style={{
                marginBottom: "10px",
                fontSize: "14px",
                color: theme.colors.textSecondary,
              }}
            >
              Rehearsals coordination with MC and/or moderators/speakers as
              required.
            </li>
            <li
              style={{
                marginBottom: "10px",
                fontSize: "14px",
                color: theme.colors.textSecondary,
              }}
            >
              Collection of flight, hotel and logistics details and preference.
            </li>
            <li
              style={{
                marginBottom: "10px",
                fontSize: "14px",
                color: theme.colors.textSecondary,
              }}
            >
              Coordination on Visa requirements if applicable.
            </li>
            <li
              style={{
                marginBottom: "10px",
                fontSize: "14px",
                color: theme.colors.textSecondary,
              }}
            >
              Coordination with drivers and ground transportation agency for
              pick-up and drop off (Airport - Hotel, Hotel Venue).
            </li>
            <li
              style={{
                marginBottom: "10px",
                fontSize: "14px",
                color: theme.colors.textSecondary,
              }}
            >
              Speakers attaché handling speakers experience on site and
              logistical assistance as required.
            </li>
          </ul>
        </div>

        <div style={footerStyle}>
          <span>Speaker Agency | Case Study - Mawhiba</span>
          <span>
            Page {++currentPage} of {totalPages}
          </span>
        </div>
      </div>

      {/* CASE STUDY PAGE 5: NSTI Family Festival */}
      <div className="pdf-page" style={pageStyle}>
        <div style={sideImageStyle}>
          <div
            style={{
              width: "20mm",
              height: "25mm",
              backgroundColor: "white",
              marginBottom: "10mm",
              borderRadius: "4px",
            }}
          ></div>
          <div
            style={{
              width: "20mm",
              height: "25mm",
              backgroundColor: "white",
              marginBottom: "10mm",
              borderRadius: "4px",
            }}
          ></div>
          <div
            style={{
              width: "20mm",
              height: "25mm",
              backgroundColor: "white",
              marginBottom: "10mm",
              borderRadius: "4px",
            }}
          ></div>
          <div
            style={{
              width: "20mm",
              height: "25mm",
              backgroundColor: "white",
              marginBottom: "10mm",
              borderRadius: "4px",
            }}
          ></div>
          <div
            style={{
              width: "20mm",
              height: "25mm",
              backgroundColor: "white",
              marginBottom: "10mm",
              borderRadius: "4px",
            }}
          ></div>
        </div>

        <div style={mainContentStyle}>
          <h1 style={h1Style}>Case Study</h1>
          <h2 style={h2Style}>NSTI Family Festival In Dubai</h2>
          <p
            style={{
              fontSize: "16px",
              color: theme.colors.textSecondary,
              lineHeight: 1.6,
              marginBottom: "30px",
            }}
          >
            The National Science, Technology, and Innovation Festival (NSTI)
            champions the spirit of discovery and innovation, setting a stage
            for nurturing passions in science, technology, innovation, and
            entrepreneurship. This festival invites attendees to dive into
            learning, sharing, and collaboration, backed by the solid foundation
            of scientific research.
          </p>

          <h2 style={h2Style}>Results</h2>
          <ul style={{ paddingLeft: "20px", margin: "0 0 20px 0" }}>
            <li
              style={{
                marginBottom: "10px",
                fontSize: "14px",
                color: theme.colors.textSecondary,
              }}
            >
              Spotlighted over 100 innovative projects from high schools across
              the UAE.
            </li>
            <li
              style={{
                marginBottom: "10px",
                fontSize: "14px",
                color: theme.colors.textSecondary,
              }}
            >
              Featured 28 speakers who contributed to a comprehensive discourse
              on science, technology, innovation, and entrepreneurship.
            </li>
            <li
              style={{
                marginBottom: "10px",
                fontSize: "14px",
                color: theme.colors.textSecondary,
              }}
            >
              Engaged early learners through the Young Innovators Competition,
              promoting entrepreneurial and innovative thinking from KG1 to
              Grade 4.
            </li>
            <li
              style={{
                marginBottom: "10px",
                fontSize: "14px",
                color: theme.colors.textSecondary,
              }}
            >
              Brought together a diverse group of speakers, including Aysha
              Alobeidli, the youngest Emirati Chef, alongside entrepreneurs and
              government officials, to inspire the community towards positive
              global impact.
            </li>
            <li
              style={{
                marginBottom: "10px",
                fontSize: "14px",
                color: theme.colors.textSecondary,
              }}
            >
              The Family Festival, featuring Art, Food, and Edutainment
              Districts, fostered community interaction and promoted
              edutainment, enhancing engagement.
            </li>
          </ul>
        </div>

        <div style={footerStyle}>
          <span>Speaker Agency | Case Study - NSTI Family Festival</span>
          <span>
            Page {++currentPage} of {totalPages}
          </span>
        </div>
      </div>

      {/* PAGE: THANK YOU & CONTACT */}
      <div
        className="pdf-page"
        style={{
          ...pageStyle,
          textAlign: "center",
          justifyContent: "center",
          backgroundImage: "url(/public/images/background.jpeg)",
          backgroundSize: "cover",
          backgroundPosition: "center",
        }}
      >
        <div
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: "rgba(0,0,0,0.7)",
          }}
        ></div>

        <div
          style={{
            position: "relative",
            zIndex: 1,
            display: "flex",
            width: "100%",
            gap: "40mm",
          }}
        >
          {/* Left Column */}
          <div style={{ flex: 1, textAlign: "left", color: "white" }}>
            <div
              style={{
                display: "flex",
                alignItems: "center",
                marginBottom: "20px",
              }}
            >
              <div
                style={{
                  width: "20mm",
                  height: "20mm",
                  backgroundColor: "white",
                  borderRadius: "50%",
                  marginRight: "10mm",
                }}
              ></div>
              <h1 style={{ fontSize: "36px", fontWeight: "bold", margin: 0 }}>
                MENA speakers
              </h1>
            </div>
            <p style={{ fontSize: "18px", margin: 0, opacity: 0.9 }}>
              The #1 Speakers Bureau in the MENA Region since 2016
            </p>
          </div>

          {/* Right Column */}
          <div style={{ flex: 1, textAlign: "left", color: "white" }}>
            <h1
              style={{
                fontSize: "42px",
                fontWeight: "bold",
                margin: "0 0 20px 0",
              }}
            >
              Thank you
            </h1>
            <p style={{ fontSize: "18px", margin: "0 0 30px 0", opacity: 0.9 }}>
              Book professional speakers, MCs, moderators
            </p>

            <div style={{ marginBottom: "20px" }}>
              <p
                style={{ fontSize: "14px", margin: "0 0 10px 0", opacity: 0.8 }}
              >
                successful partners of
              </p>
              <div
                style={{ display: "flex", gap: "10mm", marginBottom: "20px" }}
              >
                <div
                  style={{
                    width: "15mm",
                    height: "8mm",
                    backgroundColor: "white",
                    borderRadius: "4px",
                  }}
                ></div>
                <div
                  style={{
                    width: "15mm",
                    height: "8mm",
                    backgroundColor: "white",
                    borderRadius: "4px",
                  }}
                ></div>
                <div
                  style={{
                    width: "15mm",
                    height: "8mm",
                    backgroundColor: "white",
                    borderRadius: "4px",
                  }}
                ></div>
              </div>
            </div>

            <h2
              style={{
                fontSize: "24px",
                fontWeight: "bold",
                margin: "0 0 20px 0",
              }}
            >
              Contact
            </h2>
            <div style={{ fontSize: "16px", lineHeight: 1.6 }}>
              <p style={{ margin: "0 0 10px 0" }}>Abdulghafour Alsamman</p>
              <p style={{ margin: "0 0 10px 0" }}>
                Email: <EMAIL>
              </p>
              <p style={{ margin: "0 0 10px 0" }}>Phone: +966 504456649</p>
              <p style={{ margin: "0 0 10px 0" }}>
                Website:{" "}
                <span style={{ color: "#4A90E2" }}>www.mena-speakers.com</span>
              </p>
            </div>
          </div>
        </div>

        <div style={footerStyle}>
          <span>Speaker Agency | Contact</span>
          <span>
            Page {++currentPage} of {totalPages}
          </span>
        </div>
      </div>
    </div>
  );
};
