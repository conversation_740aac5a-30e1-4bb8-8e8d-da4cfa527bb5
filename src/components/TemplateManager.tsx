import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Plus, Edit, Trash2, Copy, Palette } from "lucide-react";
import { Tables } from "@/integrations/supabase/types";
import { AdvancedTemplateBuilder } from "./AdvancedTemplateBuilder";

type ProposalTemplate = Tables<"proposal_templates">;

interface TemplateManagerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSelectTemplate?: (template: ProposalTemplate) => void;
  mode?: "select" | "manage";
}

export function TemplateManager({ 
  open, 
  onOpenChange, 
  onSelectTemplate,
  mode = "manage" 
}: TemplateManagerProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [editingTemplate, setEditingTemplate] = useState<ProposalTemplate | null>(null);
  const [showTemplateBuilder, setShowTemplateBuilder] = useState(false);
  const [newTemplateName, setNewTemplateName] = useState("");
  const [showNameDialog, setShowNameDialog] = useState(false);

  const { data: templates, isLoading } = useQuery({
    queryKey: ['proposalTemplates'],
    queryFn: async () => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return [];

      const { data, error } = await supabase
        .from('proposal_templates')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data;
    },
  });

  const deleteTemplateMutation = useMutation({
    mutationFn: async (templateId: string) => {
      const { error } = await supabase
        .from('proposal_templates')
        .delete()
        .eq('id', templateId);
      
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['proposalTemplates'] });
      toast({ title: 'Template deleted successfully!' });
    },
    onError: (error) => {
      toast({ 
        title: 'Error deleting template', 
        description: error.message, 
        variant: 'destructive' 
      });
    },
  });

  const duplicateTemplateMutation = useMutation({
    mutationFn: async (template: ProposalTemplate) => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('You must be logged in');

      const { id, created_at, updated_at, ...templateData } = template;
      const { error } = await supabase
        .from('proposal_templates')
        .insert({
          ...templateData,
          name: `${template.name} (Copy)`,
          user_id: user.id,
        });
      
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['proposalTemplates'] });
      toast({ title: 'Template duplicated successfully!' });
    },
    onError: (error) => {
      toast({ 
        title: 'Error duplicating template', 
        description: error.message, 
        variant: 'destructive' 
      });
    },
  });

  const handleCreateNew = () => {
    setShowNameDialog(true);
  };

  const handleCreateTemplate = () => {
    if (!newTemplateName.trim()) {
      toast({ title: 'Please enter a template name', variant: 'destructive' });
      return;
    }
    setEditingTemplate(null);
    setShowNameDialog(false);
    setShowTemplateBuilder(true);
  };

  const handleEdit = (template: ProposalTemplate) => {
    setEditingTemplate(template);
    setShowTemplateBuilder(true);
  };

  const handleTemplateBuilderClose = () => {
    setShowTemplateBuilder(false);
    setEditingTemplate(null);
    setNewTemplateName("");
  };

  const handleSelect = (template: ProposalTemplate) => {
    if (onSelectTemplate) {
      onSelectTemplate(template);
      onOpenChange(false);
    }
  };

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Palette className="h-5 w-5" />
              {mode === "select" ? "Select Template" : "Manage Templates"}
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-4 overflow-y-auto max-h-[60vh]">
            {mode === "manage" && (
              <Button 
                onClick={handleCreateNew} 
                className="w-full mb-4"
                variant="outline"
              >
                <Plus className="w-4 h-4 mr-2" />
                Create New Template
              </Button>
            )}

            {isLoading ? (
              <div className="text-center py-8">Loading templates...</div>
            ) : templates?.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                {mode === "select" 
                  ? "No templates available. Create one first."
                  : "No templates yet. Create your first template!"
                }
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {templates?.map((template) => (
                  <Card 
                    key={template.id} 
                    className={`cursor-pointer transition-all hover:shadow-md ${
                      mode === "select" ? "hover:bg-muted/50" : ""
                    }`}
                    onClick={() => mode === "select" && handleSelect(template)}
                  >
                    <CardHeader className="pb-2">
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-lg">{template.name}</CardTitle>
                        <Badge variant="secondary">{template.layout_style}</Badge>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2 mb-4">
                        <div className="flex items-center gap-2">
                          <div 
                            className="w-4 h-4 rounded border"
                            style={{ backgroundColor: template.primary_color }}
                          />
                          <span className="text-sm text-muted-foreground">
                            {template.primary_color}
                          </span>
                        </div>
                        <p className="text-sm text-muted-foreground line-clamp-2">
                          {template.about_us_mission}
                        </p>
                      </div>
                      
                      {mode === "manage" && (
                        <div className="flex gap-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleEdit(template);
                            }}
                          >
                            <Edit className="w-3 h-3 mr-1" />
                            Edit
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={(e) => {
                              e.stopPropagation();
                              duplicateTemplateMutation.mutate(template);
                            }}
                          >
                            <Copy className="w-3 h-3 mr-1" />
                            Copy
                          </Button>
                          <Button
                            size="sm"
                            variant="destructive"
                            onClick={(e) => {
                              e.stopPropagation();
                              if (confirm('Are you sure you want to delete this template?')) {
                                deleteTemplateMutation.mutate(template.id);
                              }
                            }}
                          >
                            <Trash2 className="w-3 h-3" />
                          </Button>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Name Dialog */}
      <Dialog open={showNameDialog} onOpenChange={setShowNameDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create New Template</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div>
              <Label htmlFor="template-name">Template Name</Label>
              <Input
                id="template-name"
                value={newTemplateName}
                onChange={(e) => setNewTemplateName(e.target.value)}
                placeholder="e.g., Corporate Proposal, Modern Layout..."
              />
            </div>
            <div className="flex gap-2 justify-end">
              <Button variant="outline" onClick={() => setShowNameDialog(false)}>
                Cancel
              </Button>
              <Button onClick={handleCreateTemplate}>
                Create Template
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Template Builder */}
      <AdvancedTemplateBuilder
        open={showTemplateBuilder}
        onOpenChange={handleTemplateBuilderClose}
        template={editingTemplate}
        templateName={newTemplateName}
      />
    </>
  );
}