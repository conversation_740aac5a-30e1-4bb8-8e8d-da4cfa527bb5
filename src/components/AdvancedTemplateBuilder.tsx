import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, <PERSON>Title } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { Loader2, Save, Palette, FileText } from 'lucide-react';
import { Tables } from '@/integrations/supabase/types';
import { PageEditor } from './PageEditor';
import { PagePreview } from './PagePreview';

type ProposalTemplate = Tables<"proposal_templates">;

const templateSchema = z.object({
  name: z.string().min(1, 'Template name is required'),
  cover_page_title: z.string().min(1, 'Cover title is required'),
  cover_page_subtitle: z.string().optional(),
  cover_page_image_url: z.string().url().optional().or(z.literal('')),
  about_us_mission: z.string().min(1, 'About us text is required'),
  
  // Colors
  primary_color: z.string().regex(/^#[0-9A-F]{6}$/i, 'Must be a valid hex color'),
  secondary_color: z.string().regex(/^#[0-9A-F]{6}$/i, 'Must be a valid hex color'),
  accent_color: z.string().regex(/^#[0-9A-F]{6}$/i, 'Must be a valid hex color'),
  text_color: z.string().regex(/^#[0-9A-F]{6}$/i, 'Must be a valid hex color'),
  background_color: z.string().regex(/^#[0-9A-F]{6}$/i, 'Must be a valid hex color'),
  
  // Typography
  heading_font: z.string(),
  body_font: z.string(),
  font_size_base: z.number().min(8).max(24),
  line_height: z.number().min(1).max(3),
  
  // Layout
  page_margin: z.number().min(10).max(50),
  section_spacing: z.number().min(5).max(30),
  header_height: z.number().min(40).max(120),
  footer_height: z.number().min(30).max(80),
  
  // Page Structure
  include_cover_page: z.boolean(),
  include_about_page: z.boolean(),
  include_event_details: z.boolean(),
  include_speaker_profiles: z.boolean(),
  include_investment_summary: z.boolean(),
  include_thank_you_page: z.boolean(),
  
  // Content Options
  show_speaker_images: z.boolean(),
  show_speaker_bios: z.boolean(),
  show_speaker_rates: z.boolean(),
  show_company_logo: z.boolean(),
  watermark_text: z.string().optional(),
  
  // Advanced Layout
  layout_style: z.enum(['classic', 'modern', 'minimal', 'creative']),
  speaker_layout: z.enum(['grid', 'list', 'cards']),
  image_style: z.enum(['square', 'circle', 'rounded']),
});

type TemplateFormValues = z.infer<typeof templateSchema>;

interface PageContent {
  title: string;
  subtitle?: string;
  content: string;
  images: Array<{
    id: string;
    url: string;
    position: 'top-left' | 'top-right' | 'center' | 'bottom-left' | 'bottom-right' | 'background';
    size: 'small' | 'medium' | 'large' | 'full';
  }>;
}

interface AdvancedTemplateBuilderProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  template?: ProposalTemplate | null;
  templateName?: string;
}

const fontOptions = [
  { value: 'Arial, sans-serif', label: 'Arial' },
  { value: 'Helvetica, sans-serif', label: 'Helvetica' },
  { value: 'Georgia, serif', label: 'Georgia' },
  { value: 'Times New Roman, serif', label: 'Times New Roman' },
  { value: 'Roboto, sans-serif', label: 'Roboto' },
  { value: 'Open Sans, sans-serif', label: 'Open Sans' },
  { value: 'Montserrat, sans-serif', label: 'Montserrat' },
  { value: 'Playfair Display, serif', label: 'Playfair Display' },
];

export function AdvancedTemplateBuilder({ 
  open, 
  onOpenChange, 
  template,
  templateName = ""
}: AdvancedTemplateBuilderProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [currentPageTab, setCurrentPageTab] = useState('cover');
  
  // Initialize with default page contents
  const defaultPageContents: Record<string, PageContent> = {
    cover: {
      title: 'Speaker Proposal',
      subtitle: 'Professional Speaker Services',
      content: 'Welcome to our speaker proposal presentation.',
      images: []
    },
    about: {
      title: 'About Us',
      subtitle: 'Our Mission',
      content: 'We are dedicated to providing exceptional speaking services that inspire, educate, and transform audiences worldwide.',
      images: []
    },
    event: {
      title: 'Event Details',
      subtitle: 'Event Information',
      content: 'Detailed information about your event will be displayed here.',
      images: []
    },
    speakers: {
      title: 'Speaker Profiles',
      subtitle: 'Meet Our Speakers',
      content: 'Our carefully selected speakers bring expertise and inspiration to your event.',
      images: []
    },
    investment: {
      title: 'Investment Summary',
      subtitle: 'Investment Details',
      content: 'Investment breakdown and pricing information for your event.',
      images: []
    },
    thanks: {
      title: 'Thank You',
      subtitle: 'We Look Forward to Working With You',
      content: 'Thank you for considering our speakers for your event. We look forward to making your event a success.',
      images: []
    }
  };

  // Page content states
  const [pageContents, setPageContents] = useState<Record<string, PageContent>>(defaultPageContents);

  const form = useForm<TemplateFormValues>({
    resolver: zodResolver(templateSchema),
    defaultValues: {
      name: templateName || 'New Template',
      cover_page_title: 'Speaker Proposal',
      cover_page_subtitle: '',
      cover_page_image_url: '',
      about_us_mission: 'We are dedicated to providing exceptional speaking services that inspire, educate, and transform audiences worldwide.',
      primary_color: '#3B82F6',
      secondary_color: '#1E40AF',
      accent_color: '#F59E0B',
      text_color: '#1F2937',
      background_color: '#FFFFFF',
      heading_font: 'Montserrat, sans-serif',
      body_font: 'Open Sans, sans-serif',
      font_size_base: 12,
      line_height: 1.5,
      page_margin: 20,
      section_spacing: 15,
      header_height: 80,
      footer_height: 50,
      include_cover_page: true,
      include_about_page: true,
      include_event_details: true,
      include_speaker_profiles: true,
      include_investment_summary: true,
      include_thank_you_page: true,
      show_speaker_images: true,
      show_speaker_bios: true,
      show_speaker_rates: true,
      show_company_logo: true,
      watermark_text: '',
      layout_style: 'modern',
      speaker_layout: 'cards',
      image_style: 'rounded',
    },
  });

  useEffect(() => {
    if (template) {
      console.log('Loading existing template:', template);
      
      // Reset form with template data
      form.reset({
        name: template.name,
        cover_page_title: template.cover_page_title,
        cover_page_subtitle: template.cover_page_subtitle || '',
        cover_page_image_url: template.cover_page_image_url || '',
        about_us_mission: template.about_us_mission,
        primary_color: template.primary_color,
        secondary_color: template.secondary_color,
        accent_color: template.accent_color,
        text_color: template.text_color,
        background_color: template.background_color,
        heading_font: template.heading_font,
        body_font: template.body_font,
        font_size_base: template.font_size_base,
        line_height: template.line_height,
        page_margin: template.page_margin,
        section_spacing: template.section_spacing,
        header_height: template.header_height,
        footer_height: template.footer_height,
        include_cover_page: template.include_cover_page,
        include_about_page: template.include_about_page,
        include_event_details: template.include_event_details,
        include_speaker_profiles: template.include_speaker_profiles,
        include_investment_summary: template.include_investment_summary,
        include_thank_you_page: template.include_thank_you_page,
        show_speaker_images: template.show_speaker_images,
        show_speaker_bios: template.show_speaker_bios,
        show_speaker_rates: template.show_speaker_rates,
        show_company_logo: template.show_company_logo,
        watermark_text: template.watermark_text || '',
        layout_style: template.layout_style as 'classic' | 'modern' | 'minimal' | 'creative',
        speaker_layout: template.speaker_layout as 'grid' | 'list' | 'cards',
        image_style: template.image_style as 'square' | 'circle' | 'rounded',
      });

      // Load page contents from template with proper error handling
      if (template.page_contents) {
        try {
          let parsedContents;
          
          if (typeof template.page_contents === 'string') {
            parsedContents = JSON.parse(template.page_contents);
          } else {
            parsedContents = template.page_contents;
          }
          
          console.log('Parsed page contents:', parsedContents);
          
          if (parsedContents && typeof parsedContents === 'object') {
            // Merge with defaults to ensure all pages exist
            const mergedContents = { ...defaultPageContents };
            Object.keys(parsedContents).forEach(key => {
              if (parsedContents[key]) {
                mergedContents[key] = {
                  ...defaultPageContents[key],
                  ...parsedContents[key]
                };
              }
            });
            setPageContents(mergedContents);
          } else {
            console.warn('Page contents not in expected format, using defaults');
            setPageContents(defaultPageContents);
          }
        } catch (error) {
          console.error('Error parsing page contents, using defaults:', error);
          setPageContents(defaultPageContents);
        }
      } else {
        console.log('No page contents found, using defaults');
        setPageContents(defaultPageContents);
      }
    } else if (templateName) {
      form.setValue('name', templateName);
      setPageContents(defaultPageContents);
    } else {
      // New template - reset to defaults
      setPageContents(defaultPageContents);
    }
  }, [template, templateName, form]);

  const updatePageContent = (pageKey: string, content: PageContent) => {
    setPageContents(prev => ({ ...prev, [pageKey]: content }));
  };

  const watchedValues = form.watch();

  const getPageTabs = () => {
    const allPages = [
      { key: 'cover', label: 'Cover Page', enabled: watchedValues.include_cover_page },
      { key: 'about', label: 'About Us', enabled: watchedValues.include_about_page },
      { key: 'event', label: 'Event Details', enabled: watchedValues.include_event_details },
      { key: 'speakers', label: 'Speakers', enabled: watchedValues.include_speaker_profiles },
      { key: 'investment', label: 'Investment', enabled: watchedValues.include_investment_summary },
      { key: 'thanks', label: 'Thank You', enabled: watchedValues.include_thank_you_page },
    ];
    return allPages.filter(page => page.enabled);
  };

  const saveTemplateMutation = useMutation({
    mutationFn: async (values: TemplateFormValues) => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('You must be logged in to save templates.');

      const templateData = {
        name: values.name,
        cover_page_title: values.cover_page_title,
        cover_page_subtitle: values.cover_page_subtitle,
        cover_page_image_url: values.cover_page_image_url,
        about_us_mission: values.about_us_mission,
        primary_color: values.primary_color,
        secondary_color: values.secondary_color,
        accent_color: values.accent_color,
        text_color: values.text_color,
        background_color: values.background_color,
        heading_font: values.heading_font,
        body_font: values.body_font,
        font_size_base: values.font_size_base,
        line_height: values.line_height,
        page_margin: values.page_margin,
        section_spacing: values.section_spacing,
        header_height: values.header_height,
        footer_height: values.footer_height,
        include_cover_page: values.include_cover_page,
        include_about_page: values.include_about_page,
        include_event_details: values.include_event_details,
        include_speaker_profiles: values.include_speaker_profiles,
        include_investment_summary: values.include_investment_summary,
        include_thank_you_page: values.include_thank_you_page,
        show_speaker_images: values.show_speaker_images,
        show_speaker_bios: values.show_speaker_bios,
        show_speaker_rates: values.show_speaker_rates,
        show_company_logo: values.show_company_logo,
        watermark_text: values.watermark_text,
        layout_style: values.layout_style,
        speaker_layout: values.speaker_layout,
        image_style: values.image_style,
        page_contents: pageContents as any, // Cast to any to satisfy Json type
        user_id: user.id,
      };

      if (template) {
        const { error } = await supabase
          .from('proposal_templates')
          .update(templateData)
          .eq('id', template.id);
        if (error) throw error;
      } else {
        const { error } = await supabase
          .from('proposal_templates')
          .insert(templateData);
        if (error) throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['proposalTemplates'] });
      toast({ title: template ? 'Template updated successfully!' : 'Template created successfully!' });
      onOpenChange(false);
    },
    onError: (error) => {
      toast({ title: 'Error saving template', description: error.message, variant: 'destructive' });
    },
  });

  const onSubmit = (values: TemplateFormValues) => {
    saveTemplateMutation.mutate(values);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-[98vw] max-h-[95vh] overflow-hidden">
        <DialogHeader className="pb-4">
          <DialogTitle className="flex items-center gap-3 text-2xl font-semibold">
            <div className="p-2 rounded-md bg-primary/10">
              <Palette className="h-6 w-6 text-primary" />
            </div>
            {template ? 'Edit Template' : 'Create New Template'}
          </DialogTitle>
          <DialogDescription className="text-base">
            Design your proposal template with custom layouts, colors, fonts, and page content.
          </DialogDescription>
        </DialogHeader>

        <div className="flex h-[82vh] gap-6">
          {/* Left Panel - Settings */}
          <div className="w-1/4 overflow-y-auto pr-2">
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                <Tabs defaultValue="general" className="w-full">
                  <TabsList className="grid w-full grid-cols-3">
                    <TabsTrigger value="general">General</TabsTrigger>
                    <TabsTrigger value="style">Style</TabsTrigger>
                    <TabsTrigger value="structure">Pages</TabsTrigger>
                  </TabsList>

                  <TabsContent value="general" className="space-y-4">
                    <Card>
                      <CardHeader>
                        <CardTitle>Template Information</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <FormField
                          control={form.control}
                          name="name"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Template Name</FormLabel>
                              <FormControl>
                                <Input placeholder="e.g., Corporate Proposal" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </CardContent>
                    </Card>
                  </TabsContent>

                  <TabsContent value="style" className="space-y-4">
                    <Card>
                      <CardHeader>
                        <CardTitle>Color Scheme</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        {[
                          { name: 'primary_color', label: 'Primary Color', default: '#3B82F6' },
                          { name: 'secondary_color', label: 'Secondary Color', default: '#1E40AF' },
                          { name: 'accent_color', label: 'Accent Color', default: '#F59E0B' },
                          { name: 'text_color', label: 'Text Color', default: '#1F2937' },
                          { name: 'background_color', label: 'Background Color', default: '#FFFFFF' },
                        ].map((color) => (
                          <FormField
                            key={color.name}
                            control={form.control}
                            name={color.name as keyof TemplateFormValues}
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>{color.label}</FormLabel>
                                <div className="flex gap-2">
                                  <FormControl>
                                    <Input type="color" value={field.value as string} onChange={field.onChange} className="w-16 h-10 p-1" />
                                  </FormControl>
                                  <FormControl>
                                    <Input value={field.value as string} onChange={field.onChange} placeholder={color.default} />
                                  </FormControl>
                                </div>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        ))}
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader>
                        <CardTitle>Typography Settings</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="grid grid-cols-2 gap-4">
                          <FormField
                            control={form.control}
                            name="heading_font"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Heading Font</FormLabel>
                                <Select onValueChange={field.onChange} value={field.value}>
                                  <FormControl>
                                    <SelectTrigger>
                                      <SelectValue />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    {fontOptions.map((font) => (
                                      <SelectItem key={font.value} value={font.value}>
                                        {font.label}
                                      </SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name="body_font"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Body Font</FormLabel>
                                <Select onValueChange={field.onChange} value={field.value}>
                                  <FormControl>
                                    <SelectTrigger>
                                      <SelectValue />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    {fontOptions.map((font) => (
                                      <SelectItem key={font.value} value={font.value}>
                                        {font.label}
                                      </SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>

                        <div className="grid grid-cols-2 gap-4">
                          <FormField
                            control={form.control}
                            name="font_size_base"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Font Size: {field.value}pt</FormLabel>
                                <FormControl>
                                  <Slider
                                    min={8}
                                    max={24}
                                    step={1}
                                    value={[field.value]}
                                    onValueChange={(value) => field.onChange(value[0])}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name="line_height"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Line Height: {field.value}</FormLabel>
                                <FormControl>
                                  <Slider
                                    min={1}
                                    max={3}
                                    step={0.1}
                                    value={[field.value]}
                                    onValueChange={(value) => field.onChange(value[0])}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader>
                        <CardTitle>Layout Options</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="grid grid-cols-3 gap-4">
                          <FormField
                            control={form.control}
                            name="layout_style"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Layout Style</FormLabel>
                                <Select onValueChange={field.onChange} value={field.value}>
                                  <FormControl>
                                    <SelectTrigger>
                                      <SelectValue />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    <SelectItem value="classic">Classic</SelectItem>
                                    <SelectItem value="modern">Modern</SelectItem>
                                    <SelectItem value="minimal">Minimal</SelectItem>
                                    <SelectItem value="creative">Creative</SelectItem>
                                  </SelectContent>
                                </Select>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name="speaker_layout"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Speaker Layout</FormLabel>
                                <Select onValueChange={field.onChange} value={field.value}>
                                  <FormControl>
                                    <SelectTrigger>
                                      <SelectValue />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    <SelectItem value="grid">Grid</SelectItem>
                                    <SelectItem value="list">List</SelectItem>
                                    <SelectItem value="cards">Cards</SelectItem>
                                  </SelectContent>
                                </Select>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name="image_style"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Image Style</FormLabel>
                                <Select onValueChange={field.onChange} value={field.value}>
                                  <FormControl>
                                    <SelectTrigger>
                                      <SelectValue />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    <SelectItem value="square">Square</SelectItem>
                                    <SelectItem value="circle">Circle</SelectItem>
                                    <SelectItem value="rounded">Rounded</SelectItem>
                                  </SelectContent>
                                </Select>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>

                        <Separator />

                        <div className="grid grid-cols-4 gap-4">
                          {[
                            { name: 'page_margin', label: 'Page Margin', min: 10, max: 50 },
                            { name: 'section_spacing', label: 'Section Spacing', min: 5, max: 30 },
                            { name: 'header_height', label: 'Header Height', min: 40, max: 120 },
                            { name: 'footer_height', label: 'Footer Height', min: 30, max: 80 },
                          ].map((setting) => (
                            <FormField
                              key={setting.name}
                              control={form.control}
                              name={setting.name as keyof TemplateFormValues}
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>{setting.label}: {field.value}</FormLabel>
                                  <FormControl>
                                    <Slider
                                      min={setting.min}
                                      max={setting.max}
                                      step={1}
                                      value={[Number(field.value)]}
                                      onValueChange={(value) => field.onChange(value[0])}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  </TabsContent>

                  <TabsContent value="structure" className="space-y-4">
                    <Card>
                      <CardHeader>
                        <CardTitle>Page Structure</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="grid grid-cols-2 gap-4">
                          {[
                            { name: 'include_cover_page', label: 'Cover Page' },
                            { name: 'include_about_page', label: 'About Page' },
                            { name: 'include_event_details', label: 'Event Details' },
                            { name: 'include_speaker_profiles', label: 'Speaker Profiles' },
                            { name: 'include_investment_summary', label: 'Investment Summary' },
                            { name: 'include_thank_you_page', label: 'Thank You Page' },
                          ].map((page) => (
                            <FormField
                              key={page.name}
                              control={form.control}
                              name={page.name as keyof TemplateFormValues}
                              render={({ field }) => (
                                <FormItem className="flex items-center space-x-2">
                                  <FormControl>
                                    <Switch
                                      checked={field.value as boolean}
                                      onCheckedChange={field.onChange}
                                    />
                                  </FormControl>
                                  <FormLabel className="!mt-0">{page.label}</FormLabel>
                                </FormItem>
                              )}
                            />
                          ))}
                        </div>

                        <Separator />

                        <div className="grid grid-cols-2 gap-4">
                          {[
                            { name: 'show_speaker_images', label: 'Show Speaker Images' },
                            { name: 'show_speaker_bios', label: 'Show Speaker Bios' },
                            { name: 'show_speaker_rates', label: 'Show Speaker Rates' },
                            { name: 'show_company_logo', label: 'Show Company Logo' },
                          ].map((option) => (
                            <FormField
                              key={option.name}
                              control={form.control}
                              name={option.name as keyof TemplateFormValues}
                              render={({ field }) => (
                                <FormItem className="flex items-center space-x-2">
                                  <FormControl>
                                    <Switch
                                      checked={field.value as boolean}
                                      onCheckedChange={field.onChange}
                                    />
                                  </FormControl>
                                  <FormLabel className="!mt-0">{option.label}</FormLabel>
                                </FormItem>
                              )}
                            />
                          ))}
                        </div>

                        <FormField
                          control={form.control}
                          name="watermark_text"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Watermark Text (Optional)</FormLabel>
                              <FormControl>
                                <Input placeholder="CONFIDENTIAL" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </CardContent>
                    </Card>
                  </TabsContent>
                </Tabs>

                <DialogFooter className="pt-6 border-t">
                  <Button 
                    type="button" 
                    variant="outline" 
                    onClick={() => onOpenChange(false)}
                    className="mr-3"
                  >
                    Cancel
                  </Button>
                  <Button type="submit" disabled={saveTemplateMutation.isPending}>
                    {saveTemplateMutation.isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                    <Save className="mr-2 h-4 w-4" />
                    {template ? 'Update Template' : 'Save Template'}
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          </div>

          {/* Center Panel - Page Editor */}
          <div className="w-2/5 overflow-y-auto pr-2">
            <Tabs value={currentPageTab} onValueChange={setCurrentPageTab}>
              <TabsList className="grid w-full grid-cols-3 mb-6">
                {getPageTabs().slice(0, 3).map(page => (
                  <TabsTrigger key={page.key} value={page.key} className="text-sm">
                    {page.label}
                  </TabsTrigger>
                ))}
              </TabsList>
              
              {getPageTabs().length > 3 && (
                <TabsList className="grid w-full grid-cols-3 mb-6">
                  {getPageTabs().slice(3, 6).map(page => (
                    <TabsTrigger key={page.key} value={page.key} className="text-sm">
                      {page.label}
                    </TabsTrigger>
                  ))}
                </TabsList>
              )}

              {getPageTabs().map(page => (
                <TabsContent key={page.key} value={page.key}>
                  <PageEditor
                    pageType={page.label}
                    content={pageContents[page.key] || defaultPageContents[page.key]}
                    onContentChange={(content) => updatePageContent(page.key, content)}
                    templateSettings={watchedValues}
                  />
                </TabsContent>
              ))}
            </Tabs>
          </div>

          {/* Right Panel - Live Preview */}
          <div className="w-1/3 overflow-y-auto">
            <PagePreview
              pageType={getPageTabs().find(p => p.key === currentPageTab)?.label || 'Page'}
              content={pageContents[currentPageTab] || defaultPageContents[currentPageTab]}
              templateSettings={watchedValues}
            />
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
