
import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Event } from "@/types/event";
import { Calendar, CheckCircle, XCircle, Clock } from "lucide-react";

interface EventStatsProps {
  events: Event[];
}

const EventStats = ({ events }: EventStatsProps) => {
  const totalEvents = events.length;
  const scheduledEvents = events.filter(event => event.status === 'Scheduled').length;
  const completedEvents = events.filter(event => event.status === 'Completed').length;
  const cancelledEvents = events.filter(event => event.status === 'Cancelled').length;

  const stats = [
    { title: "Total Events", value: totalEvents, icon: Calendar, color: "text-primary" },
    { title: "Scheduled", value: scheduledEvents, icon: Clock, color: "text-blue-500" },
    { title: "Completed", value: completedEvents, icon: CheckCircle, color: "text-green-500" },
    { title: "Cancelled", value: cancelledEvents, icon: XCircle, color: "text-red-500" }
  ];

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {stats.map((stat, index) => (
        <Card key={index}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{stat.title}</CardTitle>
            <stat.icon className={`h-4 w-4 ${stat.color}`} />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stat.value}</div>
             <p className="text-xs text-muted-foreground">
              {stat.title === "Total Events" ? "All time" : `Status: ${stat.title}`}
            </p>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

export default EventStats;
