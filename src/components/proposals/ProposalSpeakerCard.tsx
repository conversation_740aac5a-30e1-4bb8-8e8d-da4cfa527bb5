import { useState } from "react";
import { Speaker } from "@/types/speaker";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { MapPin, User, Upload, Edit, Image as ImageIcon } from "lucide-react";

interface ProposalSpeakerCardProps {
  speaker: Speaker;
  onSelect: (
    speaker: Speaker,
    customRate?: number,
    profileImageUrl?: string
  ) => void;
  isSelected?: boolean;
}

const ProposalSpeakerCard = ({
  speaker,
  onSelect,
  isSelected = false,
}: ProposalSpeakerCardProps) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [customRate, setCustomRate] = useState(speaker.rate || 0);
  const [profileImageUrl, setProfileImageUrl] = useState<string>("");
  const [isUploading, setIsUploading] = useState(false);

  const getAvailabilityColor = (availability: string | null) => {
    switch (availability) {
      case "Available":
        return "bg-green-100 text-green-800 border-green-200";
      case "Busy":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "Unavailable":
        return "bg-red-100 text-red-800 border-red-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const handleSelect = () => {
    onSelect(speaker, customRate, profileImageUrl || undefined);
    setIsDialogOpen(false);
  };

  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    setIsUploading(true);
    try {
      // Create a data URL for the image
      const reader = new FileReader();
      reader.onload = (event) => {
        if (event.target?.result) {
          setProfileImageUrl(event.target.result as string);
        }
      };
      reader.readAsDataURL(file);
    } catch (error) {
      console.error("Error uploading image:", error);
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <Card
      className={`transition-all duration-300 hover:shadow-lg ${
        isSelected ? "ring-2 ring-primary" : ""
      }`}
    >
      <CardContent className="p-6">
        <div className="flex flex-col items-center text-center space-y-4">
          <div className="relative">
            <div className="w-20 h-20 rounded-full bg-muted flex items-center justify-center border-4 border-background shadow-md overflow-hidden">
              {profileImageUrl ? (
                <img
                  src={profileImageUrl}
                  alt={speaker.name}
                  className="w-full h-full object-cover"
                />
              ) : (
                <User className="w-10 h-10 text-muted-foreground" />
              )}
            </div>
            {speaker.availability && (
              <Badge
                variant="outline"
                className={`absolute -bottom-2 left-1/2 transform -translate-x-1/2 text-xs ${getAvailabilityColor(
                  speaker.availability
                )}`}
              >
                {speaker.availability}
              </Badge>
            )}
          </div>

          <div className="space-y-2">
            <h3 className="text-lg font-semibold text-foreground">
              {speaker.name}
            </h3>
            {speaker.category && (
              <Badge variant="secondary" className="bg-primary/10 text-primary">
                {speaker.category}
              </Badge>
            )}
          </div>

          <p className="text-sm text-muted-foreground line-clamp-2 leading-relaxed">
            {speaker.bio}
          </p>

          <div className="space-y-1 text-xs text-muted-foreground">
            {speaker.location && (
              <div className="flex items-center justify-center space-x-1">
                <MapPin size={12} />
                <span>{speaker.location}</span>
              </div>
            )}
          </div>

          {speaker.rate && speaker.rate > 0 && (
            <div className="pt-2 border-t border-border w-full">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium text-foreground">
                  Rate:
                </span>
                <span className="text-lg font-bold text-primary">
                  ${speaker.rate.toLocaleString()}
                </span>
              </div>
            </div>
          )}

          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button className="w-full mt-4">
                {isSelected ? <Edit className="w-4 h-4 mr-2" /> : null}
                {isSelected ? "Edit Selection" : "Select Speaker"}
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>Configure Speaker Selection</DialogTitle>
              </DialogHeader>
              <div className="space-y-4 py-4">
                <div>
                  <Label htmlFor="custom-rate">
                    Custom Rate ($) - Optional
                  </Label>
                  <Input
                    id="custom-rate"
                    type="number"
                    value={customRate || ""}
                    onChange={(e) =>
                      setCustomRate(e.target.value ? Number(e.target.value) : 0)
                    }
                    placeholder="Enter custom rate (optional)"
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Leave empty if rate is not specified
                  </p>
                </div>

                <div>
                  <Label htmlFor="profile-image">
                    Upload Speaker Image (Optional)
                  </Label>
                  <div className="space-y-3">
                    <div className="flex items-center space-x-2">
                      <Upload className="w-4 h-4" />
                      <Input
                        id="profile-image"
                        type="file"
                        accept="image/*"
                        onChange={handleImageUpload}
                        disabled={isUploading}
                        className="file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary file:text-primary-foreground hover:file:bg-primary/80"
                      />
                    </div>
                    {profileImageUrl && (
                      <div className="flex justify-center">
                        <img
                          src={profileImageUrl}
                          alt="Preview"
                          className="w-16 h-16 rounded-full object-cover border"
                        />
                      </div>
                    )}
                    {isUploading && (
                      <p className="text-sm text-muted-foreground">
                        Uploading image...
                      </p>
                    )}
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">
                    Upload a custom image for this speaker in the proposal
                  </p>
                </div>

                <Button
                  onClick={handleSelect}
                  className="w-full"
                  disabled={isUploading}
                >
                  {isSelected ? "Update Selection" : "Select Speaker"}
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </CardContent>
    </Card>
  );
};

export default ProposalSpeakerCard;
