
import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Images, Layers } from 'lucide-react';
import { ContentEditor } from './editor/ContentEditor';
import { ImageUploader } from './editor/ImageUploader';
import { ImageCard } from './editor/ImageCard';

interface PageImage {
  id: string;
  url: string;
  position: 'top-left' | 'top-right' | 'center' | 'bottom-left' | 'bottom-right' | 'background';
  size: 'small' | 'medium' | 'large' | 'full';
  x?: number;
  y?: number;
}

interface PageContent {
  title: string;
  subtitle?: string;
  content: string;
  images: PageImage[];
}

interface PageEditorProps {
  pageType: string;
  content: PageContent;
  onContentChange: (content: PageContent) => void;
  templateSettings: any;
}

export function PageEditor({ pageType, content, onContentChange, templateSettings }: PageEditorProps) {
  const [draggedImage, setDraggedImage] = useState<string | null>(null);

  const handleImageUpload = (newImage: PageImage) => {
    onContentChange({
      ...content,
      images: [...content.images, newImage]
    });
  };

  const updateImagePosition = (imageId: string, position: PageImage['position']) => {
    const updatedImages = content.images.map(img =>
      img.id === imageId ? { ...img, position } : img
    );
    onContentChange({ ...content, images: updatedImages });
  };

  const updateImageSize = (imageId: string, size: PageImage['size']) => {
    const updatedImages = content.images.map(img =>
      img.id === imageId ? { ...img, size } : img
    );
    onContentChange({ ...content, images: updatedImages });
  };

  const removeImage = (imageId: string) => {
    const updatedImages = content.images.filter(img => img.id !== imageId);
    onContentChange({ ...content, images: updatedImages });
  };

  return (
    <div className="space-y-8 p-6 bg-background">
      <ContentEditor
        pageType={pageType}
        content={content}
        onContentChange={onContentChange}
      />

      <Card className="border border-muted-foreground/20 shadow-sm">
        <CardHeader className="pb-6">
          <CardTitle className="flex items-center justify-between text-xl font-semibold text-foreground">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-md bg-primary/10">
                <Images className="w-5 h-5 text-primary" />
              </div>
              <span>Page Images</span>
            </div>
            <div className="flex items-center gap-2 px-3 py-1 rounded-full bg-muted text-sm text-muted-foreground">
              <Layers className="w-4 h-4" />
              {content.images.length} image{content.images.length !== 1 ? 's' : ''}
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <ImageUploader
            onImageUploaded={handleImageUpload}
            pageType={pageType}
          />

          {content.images.length > 0 && (
            <>
              <Separator className="my-6" />
              <div className="space-y-4">
                <Label className="text-lg font-medium text-foreground">
                  Manage Images
                </Label>
                <div className="grid gap-4">
                  {content.images.map((image) => (
                    <ImageCard
                      key={image.id}
                      image={image}
                      onUpdatePosition={updateImagePosition}
                      onUpdateSize={updateImageSize}
                      onRemove={removeImage}
                      onDragStart={setDraggedImage}
                    />
                  ))}
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
