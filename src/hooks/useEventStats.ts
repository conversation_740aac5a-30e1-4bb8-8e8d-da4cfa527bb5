
import { useMemo } from 'react';
import { Event } from '../types/event';
import { Employee } from '../types/employee';

export const useEventStats = (events: Event[], employees: Employee[]) => {
    return useMemo(() => {
        if (!events) {
            return {
                totalEvents: 0,
                upcomingEvents: 0,
                completedEvents: 0,
                cancelledEvents: 0,
                recentEvents: [],
                employeesWithEvents: [],
                upcomingEventsList: [],
            };
        }

        const totalEvents = events.length;
        const upcomingEvents = events.filter(
            (event) => new Date(event.date) > new Date() && event.status === "Scheduled"
        ).length;
        const completedEvents = events.filter(
            (event) => event.status === "Completed"
        ).length;
        const cancelledEvents = events.filter(
            (event) => event.status === "Cancelled"
        ).length;

        const recentEvents = events
            .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
            .slice(0, 5);

        const employeeEventCounts = events.reduce((acc, event) => {
            if (event.responsiblePersonId) {
              acc[event.responsiblePersonId] = (acc[event.responsiblePersonId] || 0) + 1;
            }
            return acc;
        }, {} as Record<string, number>);

        const employeesWithEvents = Object.keys(employeeEventCounts)
            .map((employeeId) => {
              const employee = employees.find((emp) => emp.id === employeeId);
              if (!employee) return null;
              return {
                  ...employee,
                  eventCount: employeeEventCounts[employeeId],
              };
            })
            .filter((e): e is Employee & { eventCount: number } => e !== null);

        const upcomingEventsList = events
          .filter(
            (event) =>
              new Date(event.date) > new Date() &&
              event.status === "Scheduled"
          )
          .sort(
            (a, b) =>
              new Date(a.date).getTime() - new Date(b.date).getTime()
          )
          .slice(0, 5);

        return {
            totalEvents,
            upcomingEvents,
            completedEvents,
            cancelledEvents,
            recentEvents,
            employeesWithEvents,
            upcomingEventsList,
        };
    }, [events, employees]);
}
