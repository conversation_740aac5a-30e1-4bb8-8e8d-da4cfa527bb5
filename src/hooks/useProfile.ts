
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { Tables } from '@/integrations/supabase/types';

export const useProfile = () => {
  const { user } = useAuth();

  const fetchProfile = async (): Promise<Tables<'profiles'> | null> => {
    if (!user) return null;

    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single();

    // 'PGRST116' is the error code for 'object not found', which is expected if a profile doesn't exist yet.
    if (error && error.code !== 'PGRST116') {
      console.error('Error fetching profile:', error.message);
      throw error;
    }
    
    return data;
  };

  return useQuery({
    queryKey: ['profile', user?.id],
    queryFn: fetchProfile,
    enabled: !!user, // The query will only run if the user is authenticated.
  });
};
